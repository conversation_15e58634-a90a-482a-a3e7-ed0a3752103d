# QIPLUS

## Instalação NVM

É recomendado a instalação do NVM, pois o projeto só rodará na versão 14 do node
[https://github.com/coreybutler/nvm-windows/releases](https://www.treinaweb.com.br/blog/instalando-e-gerenciando-varias-versoes-do-node-js-com-nvm)

## Build Setup

```bash
# install node v14
nvm install 14

# use node v14
nvm use 14

# install dependencies
npm install

# serve with hot reload at https://localhost:3000
npm start

# build for production with minification
npm run build
```

## Enable SSL on localhost

Enabling SSL for your Local Webpack or React Web Server on Windows
https://medium.com/@doormasterlimited/enabling-ssl-for-your-local-webpack-or-react-web-server-on-windows-40993b1a2c17

```
Importing your server.crt file into Trust Root Authorities
On Windows, hit Windows + R to and launch “mmc” to open the Microsoft Management Console. From here we can add in the Certificates management snap-in.
Go to File -> Add/Remove Snap-In… Select “Certificates”, click “Add”. In the popup select whichever you feel is safest, though I run a VM so I just selected “Computer account”. Click “Next” / “Finish”, then finally “OK” at the bottom right of the popup.
You should now see the Certificates in your left tree view. Drill down into Certificates -> Trusted Root Certification Authorities -> Certificates
Under the top menus, click the Action -> All Tasks -> Import and import your server.crt certificate file — there may be a few options but read and next/finish your way through. Check the list of authorities to make sure “localhost” is in here. If you see it, congratulations, you now trust yourself!
Ref: https://support.microsoft.com/en-us/help/962457/what-is-mmc
```

## .ENV

Variáveis de Ambiente

```
FB_APP_ID=
FB_APP_KEY=
WEBMASTER_ID=
WEBMASTER_ACCOUNT=
MESSAGINGSENDERID=
GOOGLE_ADS_CLIENT_ID=
GOOGLE_ADS_API_KEY=
GOOGLE_ADS_DEVELOPER_TOKEN=
GOOGLE_ADS_CLIENT_SECRET=
GOOGLE_ADS_TUTORIAL_URL_VIDEO=
APP_ID=
DEV_APP_ID=
CHAT_APP_HOSTNAME=
CHAT_APP_WEBSOCKET=
CODECHAT_APP_HOSTNAME=
SNIPER_URL=
PRODUCTION_ENV=
DEVELOPMENT_ENV=
SNIPER_APP_BOTS_URL=
STARTER_PLAN_ID=
PRO_PLAN_ID=
CORTEX_PLAN_ID=
SINAPSES_PLAN_ID=
NOTAZZ_QIPLUS_API_KEY=
PAGARME_API_URL= # Adicionar para exibir o relatório
PAGARME_ENCRYPTION_KEY=
PAGARME_RECIPIENT_ID=
PAGARME_API_KEY= # Adicionar para exibir o relatório
BITLY_TOKEN=
GOOGLE_APIS_CLIENT_ID=
GOOGLE_MAPS_API_KEY= # Adicionar para exibir o relatório
MSA_API_CLIENT_ID=
REACT_APP_MAILGUN_API_URL=
REACT_APP_MAILGUN_API=
```

### Author:

Marcelo Viana

https://github.com/marceviana
