/**
 * Login Page
 */
import React, { Component } from 'react'
import { connect } from 'react-redux'
import { Link } from 'react-router-dom'

import { sTrim } from 'Helpers/helpers'
import { Alert, Form, FormGroup, Input } from 'reactstrap'

import { AppBar, Button, Fab, LinearProgress, Toolbar } from '@material-ui/core'

import QueueAnim from 'rc-queue-anim'

// app config
import AppConfig from 'Constants/AppConfig'

// redux action
import { signinUserInFirebase, signinUserWithFacebook, signinUserWithGithub, signinUserWithGoogle, signinUserWithTwitter } from 'Actions'

import { useAuth } from 'Hooks/auth'
import { langMessages } from '../../../lang'

class Signin extends Component {
  state = {
    email: '',
    errorMsg: '',
    passwordVisibility: false,
    password: '',
  }

  /**
   * On User Login
   */
  onUserLogin(e) {
    e.preventDefault()
    if (!sTrim(this.state.password, ' ') && !sTrim(this.state.email, ' ')) {
      this.setState({ errorMsg: langMessages['alerts.pleaseInformYourEmailAndPassword'] })
    } else if (!sTrim(this.state.password, ' ')) {
      this.setState({ errorMsg: langMessages['alerts.pleaseInformYourPassword'] })
    } else if (!sTrim(this.state.email, ' ')) {
      this.setState({ errorMsg: langMessages['alerts.pleaseInformYourEmail'] })
    } else {
      this.props.signinUserInFirebase(this.state, this.props.history)
    }
  }

  /**
   * On User Sign Up
   */
  onUserSignUp() {
    this.props.redirectToCheckout()
  }

  render() {
    const { email, password, errorMsg, passwordVisibility } = this.state
    const { loading, user, authUser, account } = this.props

    const displayName = user.displayName || user.firstName || (authUser || {}).displayName || ''
    const isNewAccount = !account || !account.ID
    const currentPlanId = isNewAccount ? null : account.planId
    const paymentLink =
      !isNewAccount &&
      (account.pagarme || {}).subscription &&
      ['pending_payment', 'unpaid'].includes(account.pagarme.subscription.status) &&
      account.pagarme.subscription.manage_url

    return (
      <QueueAnim type="bottom" duration={2000}>
        <div className="rct-session-wrapper">
          <div className="LinearProgress-wrap" style={{ visibility: loading ? 'visible' : 'hidden' }}>
            <LinearProgress />
          </div>
          <AppBar position="static" className="session-header">
            <Toolbar>
              <div className="container">
                <div className="d-flex justify-content-between">
                  <div className="session-logo">
                    <div className="d-block d-md-none">
                      <Link to="/">
                        <img src={AppConfig.appLogo} alt="session-logo" className="img-fluid" width="110" height="35" />
                      </Link>
                    </div>
                  </div>
                  {!currentPlanId ? (
                    <div>
                      <a className="mr-15" onClick={() => this.onUserSignUp()}>
                        {langMessages['texts.dontHaveAccount']}
                      </a>
                      <Button variant="contained" className="btn-light" onClick={() => this.onUserSignUp()}>
                        {langMessages['button.signUp']}
                      </Button>
                    </div>
                  ) : (
                    <div>
                      {!!displayName && `${langMessages['texts.hi']}, ${displayName}`}
                      {!!displayName && !!paymentLink && <strong className="mx-10"> | </strong>}
                      {!!paymentLink && (
                        <a target="_blank" rel="noopener noreferrer" className="text-white" href={paymentLink}>
                          {langMessages['payment.paymentLink']}
                        </a>
                      )}
                      <Button variant="contained" className="btn-light ml-15" onClick={() => this.onUserSignUp()}>
                        {langMessages['accounts.updateMyPlan']}
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </Toolbar>
          </AppBar>
          <div className="session-inner-wrapper">
            <div className="container">
              <div className="row row-eq-height">
                <div className="col-10 col-11 col-md-7 mr-md-0 mx-auto offset-md-4">
                  <div className="session-body text-center">
                    <div className="session-head mb-30">
                      <h2 className="font-weight-bold">{langMessages['app.welcome']}</h2>
                      <p className="mb-0">{langMessages['app.slogan']}</p>
                    </div>
                    {!!errorMsg && (
                      <Alert className="mb-15" fade={false} color="warning" isOpen={true} toggle={() => this.setState({ errorMsg: '' })}>
                        {errorMsg}
                      </Alert>
                    )}
                    <Form onSubmit={this.onUserLogin.bind(this)}>
                      <FormGroup className="has-wrapper">
                        <Input
                          type="mail"
                          value={email}
                          name="user-mail"
                          id="user-mail"
                          className="has-input input-lg"
                          placeholder={langMessages['placeholders.email']}
                          onChange={event => this.setState({ errorMsg: '', email: sTrim(event.target.value, ' ') })}

                        />
                        <span className="has-icon">
                          <i className="ti-email"></i>
                        </span>
                      </FormGroup>
                      <FormGroup className="has-wrapper">
                        <Input
                          value={password}
                          type={passwordVisibility ? 'text' : 'password'}
                          name="user-pwd"
                          id="pwd"
                          className="has-input input-lg"
                          placeholder={langMessages['placeholders.password']}
                          onChange={event => this.setState({ errorMsg: '', password: sTrim(event.target.value, ' ') })}
                        />
                        <span className="has-icon" onClick={e => this.setState({ passwordVisibility: !passwordVisibility })}>
                          <i className={!passwordVisibility ? 'fa fa-eye' : 'fa fa-eye-slash'}></i>
                        </span>
                      </FormGroup>
                      <FormGroup className="mb-15">
                        <Button type="submit" color="primary" className="btn-block text-white w-100" variant="contained" size="large">
                          {langMessages['button.signIn']}
                        </Button>
                      </FormGroup>
                    </Form>
                    <p className="mb-20 text-right">
                      <Link to="/forgot-password" className="mr-15">
                        {langMessages['texts.forgotPassword']}
                      </Link>
                    </p>
                    <p className="mb-20">{langMessages['texts.orSigninWith']}</p>
                    {/* <Fab size="small" variant="circular" className="btn-facebook mr-15 mb-20 text-white"
                                 onClick={() => this.props.signinUserWithFacebook(this.props.history)}
                              >
                                 <i className="zmdi zmdi-facebook"></i>
                              </Fab> */}
                    <Fab
                      size="small"
                      variant="circular"
                      className="btn-google mr-15 mb-20 text-white"
                      onClick={() => this.props.signinUserWithGoogle(this.props.history)}
                    >
                      <i className="zmdi zmdi-google"></i>
                    </Fab>
                    {/* <Fab size="small" variant="circular" className="btn-twitter mr-15 mb-20 text-white"
                                 onClick={() => this.props.signinUserWithTwitter(this.props.history)}
                              >
                                 <i className="zmdi zmdi-twitter"></i>
                              </Fab> */}
                    {/* <Fab size="small" variant="circular" className="btn-instagram mr-15 mb-20 text-white"
                                 onClick={() => this.props.signinUserWithGithub(this.props.history)}
                              >
                                 <i className="zmdi zmdi-github-alt"></i>
                              </Fab> */}
                    {/* <p className="text-muted">By signing up you agree to {AppConfig.brandName}</p> */}
                    <p className="mb-0">
                      <a target="_blank" href="#/terms-condition">
                        {langMessages['texts.termsAndConditions']}
                      </a>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </QueueAnim>
    )
  }
}

// map state to props
const mapStateToProps = ({ authReducer }) => {
  const { loading, user, authUser, account } = authReducer
  return { loading, user, authUser, account }
}

// Create a wrapper component that uses the hook and passes it to UserBlock
const UserBlockWithAuth = (props) => {
  const { redirectToCheckout } = useAuth()
  return <Signin {...props} redirectToCheckout={redirectToCheckout} />
}

export default connect(mapStateToProps, {
  signinUserInFirebase,
  signinUserWithFacebook,
  signinUserWithGoogle,
  signinUserWithGithub,
  signinUserWithTwitter,
})(UserBlockWithAuth)
