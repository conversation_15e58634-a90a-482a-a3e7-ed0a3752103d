import FormFields from 'Constants/FormFields'

const FormModel = {
  title: 'Campos de Formulário',
  active: true,
  description: '',
  tabs: {
    settings: {
      label: 'Configurações',
      blocks: [
        {
          name: 'config',
          label: 'Configurações',
          fields: ['hide_login', 'hide_header', 'hide_menu', 'redirect', 'append_questionnaires'],
        },
      ],
    },
    fields: {
      label: 'Campos do Formulário',
      blocks: [
        {
          name: 'fields',
          label: 'Campos',
          fields: ['form_fields'],
        },
        {
          name: 'fields_blocks',
          label: 'Blocos',
          fields: ['form_blocks'],
        },
      ],
    },
    visualComponents: {
      label: 'Visualização',
      blocks: [
        {
          name: 'image',
          label: 'Imagens',
          fields: ['header_image'],
        },
        {
          name: 'button',
          label: 'Botão',
          fields: ['button_text', 'button_color'],
        },
        {
          name: 'message',
          label: 'Mensagem de Confirmação',
          fields: ['confirm_message'],
        },
      ],
    },
    redirection: {
      label: 'Redirecionamento',
      blocks: [
        {
          name: 'blocks',
          label: 'Configurações',
          fields: ['redirect'],
        },
      ],
    },
    related: {
      label: 'Relacionados',
      blocks: [
        {
          name: 'stores',
          label: 'Lojas',
          fields: ['stores'],
        },
        {
          name: 'questionnaires',
          label: 'Questionários',
          fields: ['questionnaires'],
        },
      ],
    },
    code: {
      label: 'Código',
      blocks: [],
      fields: [],
    },
  },
  fields: [
    // --------------------------------------------------------
    // TAB -----------------------------------------------------
    // --------------------------------------------------------
    {
      key: 'field_5ca7a3ea05860',
      label: 'Esconder a barra de login QIPlus',
      name: 'hide_login',
      type: 'boolean',
      instructions: '',
      required: 0,
      conditional_logic: 0,
      wrapper: {
        width: '',
        class: '',
        id: '',
      },
      layout: 'block',
      prefix_label: 0,
      prefix_name: 0,
    },
    {
      key: 'field_5ca7a4a605861',
      label: 'Esconder o cabeçalho da Loja',
      name: 'hide_header',
      type: 'boolean',
      instructions: '',
      required: 0,
      conditional_logic: 0,
      wrapper: {
        width: '',
        class: '',
        id: '',
      },
      layout: 'block',
      prefix_label: 0,
      prefix_name: 0,
    },
    {
      key: 'field_5ca7a59f91555',
      label: 'Esconder o menu do Site',
      name: 'hide_menu',
      type: 'boolean',
      instructions: '',
      required: 0,
      conditional_logic: 0,
      wrapper: {
        width: '',
        class: '',
        id: '',
      },
      layout: 'block',
      prefix_label: 0,
      prefix_name: 0,
    },
    {
      key: 'field_5c4fa1f67dc12',
      label: 'Redirecionar Leads a outro destino após o envio do formulário?',
      name: 'redirect',
      type: 'boolean',
      instructions: '',
      required: 0,
      conditional_logic: 0,
      wrapper: {
        width: '',
        class: 'clear-left',
        id: '',
      },
      message: '',
      default_value: 0,
      ui: 1,
      ui_on_text: '',
      ui_off_text: '',
    },
    {
      key: 'field_5c1137565294c',
      label: 'Anexar questionários?',
      name: 'append_questionnaires',
      type: 'boolean',
      instructions: '',
      required: 0,
      conditional_logic: 0,
      wrapper: {
        width: '20',
        class: 'clear-left',
        id: '',
      },
      message: '',
      default_value: 0,
      ui: 1,
      ui_on_text: '',
      ui_off_text: '',
    },

    // --------------------------------------------------------
    // TAB -----------------------------------------------------
    // --------------------------------------------------------
    {
      key: 'field_5c0cacb837e16',
      label: 'Campos',
      name: 'form_fields',
      type: 'flexible',
      instructions: '',
      required: 0,
      conditional_logic: 0,
      wrapper: {
        width: '',
        class: '',
        id: '',
      },
      fields: FormFields,
      button_label: langMessages['actions.addField'],
      min: 1,
      max: '',
    },
    {
      key: 'field_5c0cb875c74a2',
      label: 'Blocos de Campos',
      name: 'form_blocks',
      type: 'flexible',
      instructions: '',
      required: 0,
      conditional_logic: 0,
      wrapper: {
        width: '',
        class: '',
        id: '',
      },
      fields: [
        {
          name: 'basicos',
          label: 'Campos Básicos',
          sub_fields: ['firstName', 'lastName', 'email', 'mobile'],
          min: '',
          max: '1',
        },
        {
          name: 'redes_sociais',
          label: 'Redes Sociais',
          sub_fields: ['facebook', 'instagram', 'twitter', 'linkedin'],
          min: '',
          max: '1',
        },
        {
          name: 'endereco',
          label: 'Endereço',
          sub_fields: ['postalCode', 'street', 'number', 'comp', 'ref', 'neighborhood', 'city', 'state'],
          min: '',
          max: '1',
        },
      ],
      button_label: 'Adicionar Bloco',
      min: '',
      max: '',
    },

    // --------------------------------------------------------
    // TAB -----------------------------------------------------
    // --------------------------------------------------------
    {
      key: 'field_5ca79c21e3092',
      label: 'Imagem de Cabeçalho',
      name: 'header_image',
      type: 'image',
      instructions: '',
      required: 0,
      conditional_logic: 0,
      wrapper: {
        width: '',
        class: '',
        id: '',
      },
      layout: 'block',
      prefix_label: 0,
      prefix_name: 0,
    },
    {
      key: 'field_5c0cd71251604',
      label: 'Texto do Botão',
      name: 'button_text',
      type: 'text',
      instructions: '',
      required: 0,
      conditional_logic: 0,
      wrapper: {
        width: '50',
        class: '',
        id: '',
      },
      default_value: 'Enviar',
      placeholder: '',
      prepend: '',
      append: '',
      maxlength: '',
    },
    {
      key: 'field_5c64f7b67f379',
      label: 'Cor do Botão',
      name: 'button_color',
      type: 'color',
      instructions: '',
      required: 0,
      conditional_logic: 0,
      wrapper: {
        width: '50',
        class: '',
        id: '',
      },
      default_value: '#000000',
    },
    {
      key: 'field_5c1564fce4240',
      label: 'Mensagem de confirmação após o envio do formulário',
      name: 'confirm_message',
      type: 'editor',
      instructions: '',
      required: 1,
      conditional_logic: [
        [
          {
            field: 'field_5c4fa1f67dc12',
            operator: '!=',
            value: '1',
          },
        ],
      ],
      wrapper: {
        width: '',
        class: '',
        id: '',
      },
      default_value: 'Obrigado!',
      tabs: 'all',
      toolbar: 'full',
      media_upload: 1,
      delay: 0,
    },

    // --------------------------------------------------------
    // TAB -----------------------------------------------------
    // --------------------------------------------------------
    {
      key: 'field_5c8fb2f89fe69',
      label: 'Redirecionamento',
      name: 'redirect',
      type: 'redirect',
      instructions: '',
      required: 0,
      conditional_logic: 0,
      wrapper: {
        width: '',
        class: 'clear-left',
        id: '',
      },
      layout: 'block',
      prefix_label: 0,
      prefix_name: 0,
    },

    // --------------------------------------------------------
    // TAB -----------------------------------------------------
    // --------------------------------------------------------
    {
      key: 'field_5c157cee1e9d2',
      label: 'Lojas',
      name: 'stores',
      type: 'relationship',
      instructions: '',
      required: 1,
      conditional_logic: 0,
      wrapper: {
        width: '',
        class: '',
        id: '',
      },
      post_type: [
        'stores',
      ],
      taxonomy: '',
      filters: '',
      elements: [
        'featured_image',
      ],
      min: 1,
      max: '',
      return_format: 'id',
    },
    {
      key: 'field_5c1130e093e36',
      label: 'Questionários',
      name: 'questionnaires',
      type: 'post_object',
      instructions: '',
      required: 0,
      conditional_logic: [
        [
          {
            field: 'field_5c1137565294c',
            operator: '==',
            value: '1',
          },
        ],
      ],
      wrapper: {
        width: '80',
        class: '',
        id: '',
      },
      post_type: [
        'questionario',
      ],
      taxonomy: '',
      allow_null: 0,
      multiple: 1,
      return_format: 'id',
      ui: 1,
    },

    // --------------------------------------------------------
    // TAB -----------------------------------------------------
    // --------------------------------------------------------
    {
      key: 'field_5c0cd02c37c59',
      label: 'Código',
      name: 'code',
      type: 'textarea',
      instructions: '',
      required: 0,
      conditional_logic: 0,
      wrapper: {
        width: '',
        class: 'readonly copy-to-clipboard',
        id: '',
      },
      default_value: '',
      placeholder: '',
      maxlength: '',
      rows: '',
      new_lines: '',
    },
  ],
}

export default FormModel
