/**
 * Auth User Reducers
 */

import {
  AUTH_USER,
  AUTH_USER_FAILURE,
  AUTH_USER_SUCCESS,
  FIRESTORE_ERROR,
  GET_ACCOUNT,
  GET_ACCOUNT_FAILURE,
  GET_ACCOUNT_OWNER,
  GET_ACCOUNT_OWNER_FAILURE,
  GET_ACCOUNT_OWNER_SUCCESS,
  GET_ACCOUNT_SUCCESS,
  GET_PENDING_UPDATES_SUCCESS,
  GET_SUBSCRIPTION,
  GET_SUBSCRIPTION_FAILURE,
  GET_SUBSCRIPTION_SUCCESS,
  INACTIVE_ACCOUNT_ALERT,
  LOGIN_USER,
  LOGIN_USER_FAILURE,
  LOGIN_USER_SUCCESS,
  LOGOUT_USER,
  PASSWORD_RESET,
  PASSWORD_RESET_FAILURE,
  PASSWORD_RESET_SUCCESS,
  RESET_ERROR_MSG,
  RESET_FIRESTORE_ERROR,
  RESET_SUCCESS_MSG,
  SAVE_ACCOUNT,
  SAVE_ACCOUNT_END,
  SAVE_ACCOUNT_FAILURE,
  SAVE_ACCOUNT_SUCCESS,
  SAVE_NEW_ACCOUNT,
  SAVE_NEW_ACCOUNT_FAILURE,
  SAVE_NEW_ACCOUNT_SUCCESS,
  SIGNUP_USER,
  SIGNUP_USER_FAILURE,
  SIGNUP_USER_SUCCESS,
  UPDATE_ACCOUNT_OWNER,
  UPDATE_USER,
  VERIFY_ACCOUNT,
  VERIFY_ACCOUNT_FAILURE,
  VERIFY_ACCOUNT_SUCCESS,
  VERIFY_USER,
  VERIFY_USER_FAILURE,
  VERIFY_USER_SUCCESS,
} from '../actions/types'

import { localJSON, sessionJSON } from 'Helpers/helpers'

import { ACCOUNTS_COLLECTION_NAME } from 'Constants/AppCollections'
import { FirebaseRepository } from 'FirebaseRef/repository'
import accountModel from 'Routes/accounts/model'
import userModel from 'Routes/qiusers/model'
/**
 * initial user
 */

const user = localJSON.get('user', null) || window.jsonClone(userModel)
const currentOwnerID = sessionJSON.get('currentOwnerID', '')
const account = sessionJSON.get('account', false) || localJSON.get('account', false) || window.jsonClone(accountModel)
const ownerId = currentOwnerID || (user || {}).owner || ''
const authUser = localJSON.get('authUser', null)

const INIT_STATE = {
  user,
  authUser,
  ownerId,
  account,
  inactiveAccount: null,
  accountOwner: null,
  fetchingAccountOwner: false,
  errorFetchingAccountOwner: false,
  fetchingSubscription: false,
  errorFetchingSubscription: false,
  savingAccount: false,
  pendingUpdate: null,
  successMsg: '',
  errorMsg: '',
  loading: false,
  error: null,
}

export default (state = INIT_STATE, action) => {
  // console.log('--------------');
  // console.log('action',action);
  // console.log('--------------');

  switch (action.type) {
    case FIRESTORE_ERROR:
      return { ...state, error: action.error }

    case RESET_FIRESTORE_ERROR:
      return { ...state, error: '' }

    case LOGIN_USER:
      return { ...state, loading: true }

    case LOGIN_USER_SUCCESS:
      let user = action.payload
      return { ...state, loading: false, user }

    case LOGIN_USER_FAILURE:
      return { ...state, loading: false }

    case AUTH_USER:
      return { ...state, loading: true }

    case AUTH_USER_SUCCESS:
      localJSON.set('authUser', action.payload)
      return { ...state, loading: false, authUser: action.payload }

    case AUTH_USER_FAILURE:
      localJSON.remove('authUser')
      return { ...state, loading: false, authUser: null }

    case VERIFY_USER:
      return { ...state, loading: true }

    case UPDATE_USER:
    case VERIFY_USER_SUCCESS:
      localJSON.set('user', action.payload)
      return { ...state, loading: false, user: action.payload }

    case VERIFY_USER_FAILURE:
      localJSON.remove('authUser')
      return { ...state, loading: false, user: window.jsonClone(userModel), authUser: null }

    case GET_ACCOUNT:
    case VERIFY_ACCOUNT:
      return { ...state, loading: true }

    case GET_ACCOUNT_SUCCESS:
    case VERIFY_ACCOUNT_SUCCESS:
      return { ...state, loading: false, account: action.payload }

    case GET_ACCOUNT_FAILURE:
    case VERIFY_ACCOUNT_FAILURE:
      return { ...state, loading: false, account: window.jsonClone(accountModel) }

    case INACTIVE_ACCOUNT_ALERT:
      return { ...state, inactiveAccount: true }

    case GET_PENDING_UPDATES_SUCCESS:
      return { ...state, pendingUpdate: action.payload }

    case SAVE_NEW_ACCOUNT:
    case SAVE_ACCOUNT:
      return { ...state, savingAccount: true }

    case SAVE_NEW_ACCOUNT_SUCCESS:
    case SAVE_ACCOUNT_SUCCESS:
      new FirebaseRepository().setDoc(`${ACCOUNTS_COLLECTION_NAME}/${action.payload.id}`, action.payload)

      return { ...state, savingAccount: false, account: action.payload }

    case SAVE_NEW_ACCOUNT_FAILURE:
      return { ...state, savingAccount: false }

    case SAVE_ACCOUNT_FAILURE:
      return { ...state, savingAccount: false }

    case SAVE_ACCOUNT_END:
      return { ...state, savingAccount: false }

    // get owners
    case GET_ACCOUNT_OWNER:
      return { ...state, fetchingAccountOwner: true, errorFetchingAccountOwner: false }

    // get owners success
    case GET_ACCOUNT_OWNER_SUCCESS:
      return { ...state, accountOwner: action.payload, fetchingAccountOwner: false, errorFetchingAccountOwner: false }

    // get owners failure
    case GET_ACCOUNT_OWNER_FAILURE:
      return { ...state, fetchingAccountOwner: false, errorFetchingAccountOwner: true }

    // get owners
    case GET_SUBSCRIPTION:
      return { ...state, loading: true, fetchingSubscription: true, errorFetchingSubscription: false }

    // get owners success
    case GET_SUBSCRIPTION_SUCCESS:
      let subscription = action.payload
      let accountData = { ...state.account, subscription, pagarme: { ...(state.account.pagarme || {}), subscription } }
      switch (subscription.status) {
        case 'paid':
        case 'trialing':
        case 'active':
          accountData.active = accountData.active
          break
        case 'pending_payment':
        case 'unpaid':
        case 'canceled':
        case 'ended':
        default:
          accountData.active = false
          break
      }
      localJSON.set('account', accountData)
      sessionJSON.get('account', {}).ID === accountData.ID && sessionJSON.set('account', accountData)
      return { ...state, account: accountData, loading: false, fetchingSubscription: false, errorFetchingSubscription: false }

    // get owners failure
    case GET_SUBSCRIPTION_FAILURE:
      return { ...state, loading: false, fetchingSubscription: false, errorFetchingSubscription: true }

    // update owner
    case UPDATE_ACCOUNT_OWNER:
      let account = action.payload
      let ownerId = account.owner
      sessionJSON.set('account', account)
      sessionJSON.set('currentOwnerID', ownerId)
      return { ...state, account, ownerId }

    case LOGOUT_USER:
      localStorage.clear()
      sessionStorage.clear()
      return { ...state, authUser: null, account: window.jsonClone(accountModel), user: window.jsonClone(userModel), ownerId: null }

    case PASSWORD_RESET:
      return { ...state, authUser: null }

    case PASSWORD_RESET_SUCCESS:
      return { ...state, loading: false, successMsg: action.payload }

    case PASSWORD_RESET_FAILURE:
      return { ...state, loading: false, errorMsg: action.payload }

    case SIGNUP_USER:
      return { ...state, loading: true }

    case SIGNUP_USER_SUCCESS:
      return { ...state, loading: false }

    case SIGNUP_USER_FAILURE:
      return { ...state, loading: false }

    case RESET_SUCCESS_MSG:
      return { ...state, successMsg: '' }

    case RESET_ERROR_MSG:
      return { ...state, errorMsg: '' }

    default:
      return { ...state }
  }
}
