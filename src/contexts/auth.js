import { logoutUserFromFirebase, signinUserWithCustomToken, singinSetupCb } from "Actions/AuthActions";
import { auth } from "FirebaseRef";
import { AuthContext } from "Hooks/auth";
import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import { AccountRepository } from "Services/account.repository";
import { CheckoutService } from '../services/checkout';

export function AuthProvider({ children }) {

  const history = useHistory()
  const dispatch = useDispatch()

  const [user, setUser] = useState(null);
  const [account, setAccount] = useState(null);
  const [qiUser, setQiUser] = useState(null);
  const [loading, setLoading] = useState(true);

  const init = async () => {
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get("token");

    if (!token) return

    const currentUser = getLocalStorage('user')
    if (currentUser && token) {
      console.log("Logout first")
      await logoutUserFromFirebase(true)(dispatch)
    }

    // Remover o token da URL
    window.history.replaceState({}, document.title, window.location.pathname);

    if (token && !user) {
      return loginWithToken(token)
    }
  };

  const updateLocalStorage = (key, value) => {
    localStorage.setItem(key, JSON.stringify(value))
  }

  const getLocalStorage = (key) => {
    return JSON.parse(localStorage.getItem(key))
  }

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      setUser(user);
      setLoading(false);
    });

    init();

    return () => unsubscribe();
  }, []);

  useEffect(() => {
    if (user) {
      updateUserAndAccount()
    }
  }, [user]);

  const updateUserAndAccount = async () => {
    const repository = new AccountRepository(user.uid)

    const account = await repository.findByOwner(user.uid)
    setAccount(account)

    const qiUser = await repository.findUserByOwner(user.uid)
    setQiUser(qiUser)

    if (account && qiUser) {
      singinSetupCb(qiUser, account, dispatch)
    }
  }

  const loginWithToken = async (token) => {
    console.log("Login with token...")
    const user = await signinUserWithCustomToken(token, history)(dispatch)
    setUser(user)
    setLoading(false)
    return user
  }

  const redirectToCheckout = async () => {
    const url = process.env.CHECKOUT_APP_URL
    if (!url) {
      console.log('Checkout app url not defined')
      return
    }
    if (!user) {
      console.log('User not logged in')
      window.location.href = url
      return
    }
    const token = await user.getIdToken()
    const checkoutService = new CheckoutService()
    const customToken = await checkoutService.getCustomToken(token)
    const checkoutUrl = `${url}?token=${customToken}`
    window.open(checkoutUrl, '_blank')
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        account,
        qiUser,
        loading,
        loginWithToken,
        redirectToCheckout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );

}
