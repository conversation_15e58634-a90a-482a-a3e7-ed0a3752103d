import { FormControl, IconButton, InputLabel, makeStyles, Select, TextField } from "@material-ui/core";
import AddIcon from '@material-ui/icons/Add';
import { langMessages } from "Lang/index";
import React from "react";
import { But<PERSON> } from "reactstrap";

const useStyles = makeStyles((theme) => ({
    label: {
        margin: theme.spacing(1),
        justifyContent: 'center',
        alignItems: 'center',
    },
    formControl: {
        margin: theme.spacing(1),
        width: '90%',
    },
    selectEmpty: {
        marginTop: theme.spacing(2),
    },
    alert: {
        width: '90%',
        margin: theme.spacing(1),
        justifyContent: 'start',
        alignItems: 'center',
    },
    compare: {
        width: '90%',
    }
}));
function CompareItens({
    listFields,
    state,
    setState,
    associatedFieldName = langMessages['modules.app.name'],
    fieldToAssociateName = langMessages['integrations.fieldToAssociate'],
    associatedFieldTitle = langMessages['integrations.fieldAssociated'],
    fieldToAssociateTitle = langMessages['integrations.fieldToAssociate'],
}) {

    const useStyles = makeStyles({

        root: {
            "& .MuiTableCell-head": {
                color: "white",
                backgroundColor: "#a9155c"
            },
        }
    });

    const onCreateBinding = () => {
        setState([...state, {
            [associatedFieldName]: "",
            [fieldToAssociateName]: ""
        }])
    }

    const deleteOnClick = (index) => {
        const newArray = [...state];
        newArray.splice(index, 1);
        setState(newArray);
    }

    const handleChange = (e, index) => {
        e.preventDefault();

        let { name, value } = e.target;
        let onChangeValue = [...state];
        onChangeValue[index][name] = value;
        setState(onChangeValue);
    };

    return (

        <div style={{ width: '100%', margin: '15px', marginTop: '20px' }}>
            {Array.isArray(state) && state.map((key, i) => {
                return (
                    <FormControl variant="outlined" style={{ width: '90%', display: 'flex', flexDirection: 'row', gap: '16px', alignItems: 'center', margin: '15px' }}>
                        <div style={{ width: '100%', display: 'flex', flexDirection: 'row', gap: '16px', alignItems: 'center' }}>

                            {
                                listFields ?
                                    <span style={{ width: "45%" }}>

                                        <InputLabel style={{ width: '100%' }}>{fieldToAssociateTitle}</InputLabel>

                                        <Select
                                            id="demo-customized-select-native"
                                            value={key[associatedFieldName]}
                                            onChange={(e) => handleChange(e, i)}
                                            name={associatedFieldName}
                                            label={associatedFieldTitle}
                                            style={{ width: '100%' }}
                                        >
                                            <option aria-label="None" value="" />
                                            {listFields.map((item, index) => {
                                                return <option key={index} style={{ width: '100%' }} value={item.name}>{item.label}</option>
                                            })}
                                        </Select>
                                    </span>
                                    :
                                    <TextField
                                        variant="outlined"
                                        label={associatedFieldTitle}
                                        name={associatedFieldName}
                                        value={key[associatedFieldName]}
                                        onChange={(e) => handleChange(e, i)}
                                        style={{ width: "45%" }}
                                    />}
                            <TextField
                                variant="outlined"
                                label={fieldToAssociateTitle}
                                name={fieldToAssociateName}
                                value={key[fieldToAssociateName]}
                                onChange={(e) => handleChange(e, i)}
                                style={{ width: "45%" }}
                            />
                            <IconButton
                                aria-label={"Eliminar"}
                                onClick={() => deleteOnClick(i)}
                                className="float-btn float-delete-btn"
                            >
                                <i className="fa fa-minus-circle text-danger"></i>
                            </IconButton>
                        </div>
                    </FormControl>
                )
            })}
            <Button color="primary"
                size="small"
                variant="contained" className="text-white"
                onClick={onCreateBinding}
                style={{ display: 'flex', alignItems: 'center', margin: '15px' }}
            >
                {langMessages['button.add']}
                <AddIcon style={{ marginLeft: '8px' }} />
            </Button>
        </div>
    )

}
export default CompareItens
