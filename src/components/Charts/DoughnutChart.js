import React, { Component } from 'react'
import { Doughnut } from 'react-chartjs-2'
import ChartConfig from '../../constants/chart-config'

// const data = {
//    labels: ['Total Request','New'],
//    datasets: [{
//       data: [250, 25],
//       backgroundColor: [
//          ChartConfig.color.primary,
//          ChartConfig.color.warning
//       ],
//       hoverBackgroundColor: [
//          ChartConfig.color.primary,
//          ChartConfig.color.warning
//       ]
//    }]
// };

const defaultOptions = {
  legend: {
    display: false,
    labels: {
      fontColor: ChartConfig.legendFontColor,
    },
  },
  cutoutPercentage: 60,
}

export default class DoughnutChart extends Component {
  render() {
    const { data, options, innerRadius, height } = this.props

    return (
      <Doughnut data={data} options={{ ...{ ...defaultOptions, cutoutPercentage: innerRadius || 60 }, ...(options || {}) }} height={height || 180} />
    )
  }
}
