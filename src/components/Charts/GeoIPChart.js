/**
 * Online Visitor
 */
import React from 'react'
import { VectorMap } from 'react-jvectormap'

// chart config
import ChartConfig from '../../constants/chart-config'

const GeoIPChart = ({ data, series, title, height, colors, zoom }) => {
  React.useEffect(() => {
    var tips = document.getElementsByClassName('jvectormap-tip')
    for (let i = 0; i < tips.length; i++) {
      tips[i].style.display = 'none'
    }
  }, [data.markers])

  return (
    <div className="card">
      {!!title && (
        <h4 className="card-title">
          <span>{title}</span>
        </h4>
      )}
      <div style={{ width: '100%', height: height || 180 }}>
        <VectorMap
          map={'world_mill'}
          backgroundColor={(colors || {}).background || ChartConfig.color.white}
          containerStyle={{
            width: '100%',
            height: '100%',
          }}
          regionStyle={{
            initial: {
              fill: (colors || {}).region || ChartConfig.color.default,
            },
          }}
          markerStyle={{
            initial: {
              fill: (colors || {}).fill || ChartConfig.color.qiplus,
              stroke: (colors || {}).stroke || ChartConfig.color.white,
            },
          }}
          series={{
            markers: [
              {
                attribute: 'fill',
                scale: (colors || {}).scale || ['#FFB70F', '#FF3B47'],
                values: data.markers.map(m => m.value),
              },
              {
                attribute: 'r',
                scale: [1, 10],
                values: data.markers.map(m => m.value),
                min: 1,
                max: 10,
              },
            ],
            ...(series || {}),
          }}
          zoomButtons={!!zoom || false}
          zoomOnScroll={!!zoom || false}
          containerClassName="map"
          markers={data.markers}
        />
      </div>
    </div>
  )
}

export default GeoIPChart
