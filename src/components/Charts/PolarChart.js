/**
 * Polar Chart
 */
import React from 'react'
import { Polar } from 'react-chartjs-2'

// chart congig
import ChartConfig from 'Constants/chart-config'

const options = {
  legend: {
    display: false,
    labels: {
      fontColor: ChartConfig.legendFontColor,
    },
  },
}

const PieChart = ({ labels, datasets, width, height }) => {
  const data = {
    labels,
    datasets,
  }
  return <Polar height={height} width={width} data={data} options={options} />
}

export default PieChart
