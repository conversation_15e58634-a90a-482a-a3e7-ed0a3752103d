/**
 * AsyncComponent
 * Code Splitting Component / Server Side Rendering
 */
import React from 'react'
import Loadable from 'react-loadable'

// rct page loader
import RctPageLoader from 'Components/RctPageLoader/RctPageLoader'
import AppModules from '../../constants/AppModules'

/*---------------- Widgets ------------------*/

const AsyncActionsBuilderComponent = Loadable({
  loader: () => import('Widgets/ActionsBuilder'),
  loading: () => <RctPageLoader />,
})

const AsyncAppTriggersBuilderComponent = Loadable({
  loader: () => import('Widgets/AppTriggersBuilder'),
  loading: () => <RctPageLoader />,
})

/*---------------- Session ------------------*/

// Session Login
const AsyncSessionLoginComponent = Loadable({
  loader: () => import('../../routes/session/login'),
  loading: () => <RctPageLoader />,
})

// Session Register
const AsyncSessionRegisterComponent = Loadable({
  loader: () => import('../../routes/session/register'),
  loading: () => <RctPageLoader />,
})

// Session Plans
const AsyncSessionPlansComponent = Loadable({
  loader: () => import('../../routes/session/plans'),
  loading: () => <RctPageLoader />,
})

// Session Lock Screen
const AsyncSessionLockScreenComponent = Loadable({
  loader: () => import('../../routes/session/lock-screen'),
  loading: () => <RctPageLoader />,
})

// Session Forgot Password
const AsyncSessionForgotPasswordComponent = Loadable({
  loader: () => import('../../routes/session/forgot-password'),
  loading: () => <RctPageLoader />,
})

// Session Player
const AsyncSessionPlayerComponent = Loadable({
  loader: () => import('../../routes/session/player'),
  loading: () => <RctPageLoader />,
})

// Session Page 404
const AsyncSessionPage404Component = Loadable({
  loader: () => import('../../routes/session/404'),
  loading: () => <RctPageLoader />,
})

// Session Page 404
const AsyncSessionPage500Component = Loadable({
  loader: () => import('../../routes/session/500'),
  loading: () => <RctPageLoader />,
})

// terms and condition
const AsyncTermsConditionComponent = Loadable({
  loader: () => import('../../routes/pages/terms-condition'),
  loading: () => <RctPageLoader />,
})

/*---------------- Reports ------------------*/
// Reports
const AsyncReportsComponent = Loadable({
  loader: () => import('../../routes/reports/default'),
  loading: () => <RctPageLoader />,
})

// Reports
const AsyncDealsReportsComponent = Loadable({
  loader: () => import('../../routes/reports/deals'),
  loading: () => <RctPageLoader />,
})

// Reports
const AsyncSellersReportsComponent = Loadable({
  loader: () => import('../../routes/reports/sellers'),
  loading: () => <RctPageLoader />,
})

// Reports
const AsyncManagersReportsComponent = Loadable({
  loader: () => import('../../routes/reports/managers'),
  loading: () => <RctPageLoader />,
})

// Reports
const AsyncTeamsReportsComponent = Loadable({
  loader: () => import('../../routes/reports/teams'),
  loading: () => <RctPageLoader />,
})

const AsyncGoogleAdsReportsComponent = Loadable({
  loader: () => import('../../routes/reports/integrations/googleads'),
  loading: () => <RctPageLoader />,
})

const AsyncFacebookAdsReportsComponent = Loadable({
  loader: () => import('../../routes/reports/integrations/facebookads'),
  loading: () => <RctPageLoader />
})

/*---------------- Dashboard ------------------*/
// default dashboard
const AsyncDashboardComponent = Loadable({
  loader: () => import('../../routes/dashboard/default'),
  loading: () => <RctPageLoader />,
})

// ecommerce dashboard
const AsyncEcommerceDashboardComponent = Loadable({
  loader: () => import('../../routes/dashboard/ecommerce'),
  loading: () => <RctPageLoader />,
})

// mail app
const AsyncMailComponent = Loadable({
  loader: () => import('../../routes/mail'),
  loading: () => <RctPageLoader />,
})

// Events
const AsyncEventsParticipantsComponent = Loadable({
  loader: () => import('../../routes/events/participants'),
  loading: () => <RctPageLoader />,
})

// Events
const AsyncEventsParticipantEditComponent = Loadable({
  loader: () => import('../../routes/events/participants/edit'),
  loading: () => <RctPageLoader />,
})

// Leads Tags
const AsyncTaxonomiesTagsComponent = Loadable({
  loader: () => import('../../routes/taxonomies/tags'),
  loading: () => <RctPageLoader />,
})

// Accounts settings
const AsyncAccountSettingsComponent = Loadable({
  loader: () => import('../../routes/accounts/settings'),
  loading: () => <RctPageLoader />,
})

const AsyncChatComponent = Loadable({
  loader: () => import('../../routes/chat/default'),
  loading: () => <RctPageLoader />,
})

const AsyncModules = {}
Object.keys(AppModules).forEach((collection, i) => {
  const { route, routes } = AppModules[collection]
  if (!route || !routes.length) return
  AsyncModules[route] = routes.reduce((acc, subRoute) => {
    return {
      ...acc,
      [subRoute]: Loadable({
        loader: () =>
          new Promise(res => {
            require([`../../routes/${route}/${subRoute}`], res)
          }),
        loading: () => <RctPageLoader />,
      }),
    }
  }, {})
})

export {
  AsyncAccountSettingsComponent, AsyncActionsBuilderComponent, AsyncAppTriggersBuilderComponent,
  AsyncChatComponent, AsyncDashboardComponent, AsyncDealsReportsComponent, AsyncEcommerceDashboardComponent, AsyncEventsParticipantEditComponent, AsyncEventsParticipantsComponent, AsyncFacebookAdsReportsComponent, AsyncGoogleAdsReportsComponent, AsyncMailComponent, AsyncManagersReportsComponent, AsyncModules, AsyncReportsComponent, AsyncSellersReportsComponent, AsyncSessionForgotPasswordComponent, AsyncSessionLockScreenComponent, AsyncSessionLoginComponent, AsyncSessionPage404Component,
  AsyncSessionPage500Component, AsyncSessionPlansComponent, AsyncSessionPlayerComponent, AsyncSessionRegisterComponent, AsyncTaxonomiesTagsComponent, AsyncTeamsReportsComponent, AsyncTermsConditionComponent
}

