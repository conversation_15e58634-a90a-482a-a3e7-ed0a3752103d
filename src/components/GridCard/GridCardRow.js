import React from "react";

export const GridCardRow = ({ childs = [] }) => {
  const align = index => (index % 2 === 0 ? "align-items-start" : "align-items-end");
  return (
    <React.Fragment>
      <div className="status-wrapper d-flex justify-content-between mt-20">
        {childs.map(({ title, subtitle }, index) => (
          <div className={`status-info mrgn-b-md col-6 ${align(index)}`} key={index}>
            <h6 className="mb-0">{title}</h6>
            <span>{subtitle}</span>
          </div>
        ))}
      </div>
    </React.Fragment>
  );
}
