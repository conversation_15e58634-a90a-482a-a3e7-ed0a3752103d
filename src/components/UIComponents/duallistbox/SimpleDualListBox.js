/**
 * SimpleDualListBox
 * Componente simplificado para seleção de itens entre duas listas
 * Não depende do Redux e faz filtragem local
 */
import {
  Avatar,
  Divider,
  InputAdornment,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  TextField
} from '@material-ui/core';
import React, { useEffect, useState } from 'react';
import { Scrollbars } from 'react-custom-scrollbars';
import { langMessages } from '../../../lang';

const SimpleDualListBox = ({
  choices = [],
  values = [],
  onChange,
  autoHeightMin = 100,
  autoHeightMax = 350,
  filterInput = true,
  renderItem,
  isLoading = false,
  onLoadMore = null,
  hasMore = false,
  isLoadingMore = false
}) => {

  // Estado para armazenar o texto de filtro
  const [filterText, setFilterText] = useState({
    choices: '',
    values: ''
  });

  // Estado para controlar a paginação do scroll
  const [scrollPages, setScrollPages] = useState({
    choices: 1,
    values: 1
  });

  // Efeito para resetar a paginação quando as escolhas ou valores mudam
  useEffect(() => {
    // Verificar se todos os valores são strings
    const allValuesAreStrings = values.every(v => typeof v === 'string');
    if (!allValuesAreStrings) {
      // Converter valores para strings se necessário
      const stringValues = values.map(v => v ? v.toString() : '').filter(v => v);

      // Atualizar os valores
      if (onChange && JSON.stringify(values) !== JSON.stringify(stringValues)) {
        onChange(stringValues);
      }
    }

    // Resetar a paginação
    setScrollPages({
      choices: 1,
      values: 1
    });

    // Resetar as flags de controle
    if (pageIncreasedRef.current) {
      pageIncreasedRef.current.choices = false;
      pageIncreasedRef.current.values = false;
    }

    // Resetar a flag de carregamento
    loadMoreRequestedRef.current = false;
  }, [choices, values, onChange]);

  // Função para filtrar itens com base no texto de busca
  const filterItem = (item, searchTerm) => {
    if (!searchTerm) return true;

    searchTerm = searchTerm.toLowerCase();
    return (
      (item.title || '').toLowerCase().includes(searchTerm) ||
      (item.subtitle || '').toLowerCase().includes(searchTerm)
    );
  };

  // Referência para o timer de debounce
  const debounceTimerRef = React.useRef(null);

  // Referência para controlar se já solicitamos mais itens
  const loadMoreRequestedRef = React.useRef(false);

  // Referência para controlar se já aumentamos a página
  const pageIncreasedRef = React.useRef({
    choices: false,
    values: false
  });

  // Efeito para limpar o timer quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // Função para lidar com o scroll com debounce simples
  const handleScrollFrame = (e, key) => {
    // Verificar se é a lista de escolhas e se há mais itens para carregar
    if (key === 'choices' && hasMore && onLoadMore && !isLoadingMore) {
      // Verificar se chegou próximo ao final da lista (85% ou mais)
      // Usar um threshold menor para garantir que o carregamento seja acionado antes de chegar ao final
      if (e.top >= 0.85) {
        // Chamar a função para carregar mais itens diretamente, sem debounce
        // para garantir que o carregamento seja acionado
        onLoadMore();
      }
    }

    // Evitar múltiplas chamadas durante o scroll para a paginação local
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Usar debounce para suavizar o comportamento do scroll para a paginação local
    debounceTimerRef.current = setTimeout(() => {
      // Aumentar a página quando chegar perto do final
      // Mas apenas se ainda não aumentamos para este scroll
      if (e.top > 0.7 && !pageIncreasedRef.current[key]) {
        // Marcar que já aumentamos a página para este scroll
        pageIncreasedRef.current[key] = true;

        // Aumentar a página
        setScrollPages(prev => ({
          ...prev,
          [key]: prev[key] + 1
        }));
      }
      // Resetar a flag quando o usuário rolar para cima
      else if (e.top < 0.6 && pageIncreasedRef.current[key]) {
        pageIncreasedRef.current[key] = false;
      }
    }, 100); // 100ms de debounce
  };

  // Função para adicionar um item à seleção
  const handleAdd = (id) => {
    // Garantir que o ID seja uma string
    const idStr = id ? id.toString() : '';

    if (idStr && !values.includes(idStr)) {
      const newValues = [...values, idStr];
      onChange(newValues);
    }
  };

  // Função para remover um item da seleção
  const handleRemove = (id) => {
    // Garantir que o ID seja uma string
    const idStr = id ? id.toString() : '';

    if (idStr) {
      const newValues = values.filter(v => v !== idStr);
      onChange(newValues);
    }
  };

  // Garantir que todos os choices tenham IDs como strings
  const choicesWithStringIds = choices.map(choice => {
    if (!choice.id) {
      return { ...choice, id: '' };
    }
    if (typeof choice.id !== 'string') {
      return { ...choice, id: choice.id.toString() };
    }
    return choice;
  });

  // Filtrar as escolhas com base no texto de filtro
  const filteredChoices = choicesWithStringIds.filter(choice =>
    filterItem(choice, filterText.choices)
  );

  // Filtrar os valores selecionados com base no texto de filtro
  const filteredValues = values
    .map(id => {
      // Garantir que o ID seja uma string para comparação
      const idStr = id ? id.toString() : '';
      if (!idStr) {
        return null;
      }

      // Encontrar o choice correspondente ao ID
      const choice = choicesWithStringIds.find(choice => {
        const choiceId = choice.id?.toString() || '';
        return choiceId === idStr;
      });

      return choice;
    })
    .filter(choice => choice && filterItem(choice, filterText.values));

  // Renderização padrão de um item
  const defaultRenderItem = (item) => (
    <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
      <div style={{ flex: 1, minWidth: 0, overflow: 'hidden', textOverflow: 'ellipsis' }}>
        <div style={{ fontWeight: 500, marginBottom: '2px' }}>{item.title}</div>
        {item.subtitle && <div className="text-muted" style={{ fontSize: '0.85em', paddingLeft: '2px' }}>{item.subtitle}</div>}
      </div>
    </div>
  );

  // Usar o renderizador personalizado ou o padrão
  const itemRenderer = renderItem || defaultRenderItem;

  return (
    <div className="d-flex duallistbox-container">
      {/* Lista de escolhas disponíveis */}
      <div className="flex-1 duallistbox-ul duallistbox-choices">
        {filterInput && (
          <div className="DualListBox-filter pl-15">
            <TextField
              fullWidth
              placeholder={langMessages['texts.filterChoices'] || "Filtrar..."}
              value={filterText.choices}
              onChange={(e) => setFilterText({ ...filterText, choices: e.target.value })}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end" className="mr-5">
                    {filterText.choices && (
                      <i
                        className="icon-close"
                        style={{ cursor: 'pointer' }}
                        onClick={() => setFilterText({ ...filterText, choices: '' })}
                      />
                    )}
                  </InputAdornment>
                ),
              }}
            />
          </div>
        )}

        <div className="pb-5 pl-15" style={{ visibility: isLoading ? 'visible' : 'hidden' }}>
          <LinearProgress style={{ height: 2, marginTop: -1 }} />
        </div>

        <Scrollbars
          className="rct-scroll"
          autoHeight
          autoHeightMin={autoHeightMin}
          autoHeightMax={autoHeightMax}
          onScrollFrame={(e) => handleScrollFrame(e, 'choices')}
        >
          <List className="p-0 list-divider">
            {filteredChoices.length > 0 ? (
              filteredChoices
                .slice(0, scrollPages.choices * 30)
                .map((choice, index) => {
                  const id = choice.id?.toString() || '';
                  // Verificar se o ID está na lista de valores selecionados
                  const isSelected = id ? values.includes(id) : false;

                  return (
                    <li
                      key={`choice-${id || index}`}
                      style={{ opacity: isSelected ? 0.5 : 1 }}
                    >
                      <ListItem
                        button
                        onClick={() => !isSelected && handleAdd(id)}
                      >
                        {choice.img ? (
                          <Avatar alt={choice.title || 'Imagem'} className="img-fluid" src={choice.img} />
                        ) : (
                          <Avatar style={{ backgroundColor: '#2196f3' }} className={choice.avatarColor || ''}>
                            {choice.icon ? <i className={choice.icon}></i> : (choice.title && choice.title.charAt(0)) || 'Q'}
                          </Avatar>
                        )}
                        <ListItemText primary={itemRenderer(choice)} />
                      </ListItem>
                      <Divider />
                    </li>
                  );
                })
            ) : (
              <div className="alert alert-linkedin m-15">
                {filterText.choices
                  ? (langMessages['components.NoItemFound'] || "Nenhum item encontrado")
                  : (langMessages['texts.noItemsAvailable'] || "Nenhum item disponível")}
              </div>
            )}

            {hasMore && (
              <div className="text-center p-10">
                {isLoadingMore ? (
                  <LinearProgress style={{ width: '50%', margin: '0 auto' }} />
                ) : (
                  <div
                    className="text-muted"
                    style={{ cursor: 'pointer', padding: '10px' }}
                    onClick={() => {
                      if (onLoadMore && !isLoadingMore) {
                        onLoadMore();
                      }
                    }}
                  >
                    Clique ou role para carregar mais
                  </div>
                )}
              </div>
            )}
          </List>
        </Scrollbars>
      </div>

      {/* Lista de itens selecionados */}
      <div className="flex-1 duallistbox-ul duallistbox-values">
        {filterInput && (
          <div className="DualListBox-filter pl-15">
            <TextField
              fullWidth
              placeholder={langMessages['texts.filterValues'] || "Filtrar selecionados..."}
              value={filterText.values}
              onChange={(e) => setFilterText({ ...filterText, values: e.target.value })}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end" className="mr-5">
                    {filterText.values && (
                      <i
                        className="icon-close"
                        style={{ cursor: 'pointer' }}
                        onClick={() => setFilterText({ ...filterText, values: '' })}
                      />
                    )}
                  </InputAdornment>
                ),
              }}
            />
          </div>
        )}

        <Scrollbars
          className="rct-scroll"
          autoHeight
          autoHeightMin={autoHeightMin}
          autoHeightMax={autoHeightMax}
          onScrollFrame={(e) => handleScrollFrame(e, 'values')}
        >
          <List className="p-0 list-divider">
            {filteredValues.length > 0 ? (
              filteredValues
                .slice(0, scrollPages.values * 30)
                .map((choice, index) => {
                  const id = choice.id?.toString() || '';

                  return (
                    <li key={`value-${id || index}`}>
                      <ListItem
                        button
                        onClick={() => handleRemove(id)}
                      >
                        {choice.img ? (
                          <Avatar alt={choice.title || 'Imagem'} className="img-fluid" src={choice.img} />
                        ) : (
                          <Avatar style={{ backgroundColor: '#2196f3' }} className={choice.avatarColor || ''}>
                            {choice.icon ? <i className={choice.icon}></i> : (choice.title && choice.title.charAt(0)) || 'Q'}
                          </Avatar>
                        )}
                        <ListItemText primary={itemRenderer(choice)} />
                      </ListItem>
                      <Divider />
                    </li>
                  );
                })
            ) : (
              <div className="alert alert-linkedin m-15">
                {filterText.values
                  ? (langMessages['components.NoItemFound'] || "Nenhum item encontrado")
                  : (langMessages['texts.noItemsSelected'] || "Nenhum item selecionado")}
              </div>
            )}
          </List>
        </Scrollbars>
      </div>
    </div>
  );
};

export default SimpleDualListBox;
