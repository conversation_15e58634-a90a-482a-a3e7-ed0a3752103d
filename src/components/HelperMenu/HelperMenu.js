import { Fab, Fade, <PERSON>, Menu, MenuItem, Tooltip } from '@material-ui/core';
import HelpOutlineIcon from '@material-ui/icons/HelpOutline';
import QuestionAnswerIcon from '@material-ui/icons/QuestionAnswer';
import WorkOutlineIcon from '@material-ui/icons/WorkOutline';
import { langMessages } from 'Lang/index';
import React from 'react';
import { SUPPORTLINK } from '../../constants/AppConstants';
export function HelperMenu(props) {
    const [anchorEl, setAnchorEl] = React.useState(null);
    const open = Boolean(anchorEl)
    const handleOpenUserDashboard = () => {
        props.openOnboarding()
    }

    const handleCloseMenu = () => {
        setAnchorEl(null);
    }

    const handleClickMenu = (event) => {
        setAnchorEl(event.currentTarget)
    }

    return (
        <div>
            <Tooltip title={langMessages["widgets.helpButtonTooltip"]}>
                <Fab onClick={(event) => handleClickMenu(event)} className="bg-qiplus" aria-label="add" style={{ position: 'fixed', right: 30, bottom: 30, zIndex: 999999 }}>
                    <HelpOutlineIcon style={{ color: "white" }} />
                </Fab>
            </Tooltip>
            <Menu
                id="simple-menu"
                anchorEl={anchorEl}
                keepMounted
                open={open}
                onClose={handleCloseMenu}
                TransitionComponent={Fade}
            >
                <MenuItem onClick={handleOpenUserDashboard}>
                    <WorkOutlineIcon style={{ marginRight: 10 }} />
                    {langMessages['button.4Steps']}
                </MenuItem>
                <MenuItem onClick={handleCloseMenu}>
                    <Link href={SUPPORTLINK} target='_blank' color="inherit" style={{ textDecoration: 'none', width: '100%' }} >
                        <QuestionAnswerIcon style={{ marginRight: 10 }} />
                        {langMessages['button.support']}
                    </Link>
                </MenuItem>
            </Menu>

        </div>)
}

