/**
 * Inset List
 */
import Avatar from '@material-ui/core/Avatar'
import List from '@material-ui/core/List'
import ListItem from '@material-ui/core/ListItem'
import ListItemText from '@material-ui/core/ListItemText'
import { langMessages } from 'Lang/index'
import React from 'react'


const selectAndUpload = (e, option, onSelect) => {
    e.preventDefault()

    const { accept } = option
    const fileInput = document.createElement('input')
    fileInput.type = 'file'

    if (accept) {
        if (typeof accept === 'string') {
            fileInput.accept = accept
        } else {
            fileInput.accept = accept.join(',')
        }
    }

    fileInput.onchange = (event) => {
        const file = event.target.files[0]
        onSelect({ file, option })
    }

    fileInput.click()
}


export const AttachMediaList = ({ options, onSelect }) => (
    <List className="m-0 list-divider">
        {options.map((option, index) => {
            const { label, icon } = option
            return <ListItem
                button
                key={index}
                onClick={e => selectAndUpload(e, option, onSelect)}
            >
                <Avatar className="bg-qiplus text-white">
                    <i className={icon}></i>
                </Avatar>
                <ListItemText inset primary={langMessages[label]} />
            </ListItem>
        })
        }
    </List>
)
