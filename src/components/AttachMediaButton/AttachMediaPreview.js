/**
 * Inset List
 */
import { Avatar, ListItem, ListItemText } from '@material-ui/core'
import React from 'react'

const previews = {
    image: (preview, name) => <img src={preview} className="w-100 h-100" alt={name} />,
    video: (preview, name) => <video src={preview} className="w-100 h-100" alt={name} controls />,
    // audio: (preview, name) => <audio src={preview} className="w-100 h-100" alt={name} controls />,
    // application: (preview, name) => <embed src={preview} type="application/pdf" className="w-100 h-100" />,
    default: (preview, name) => (
        <ListItem>
            <Avatar className="bg-qiplus text-white">
                <i className={`zmdi zmdi-file zmdi-hc-fw`}></i>
            </Avatar>
            <ListItemText inset primary={name} />
        </ListItem>
    ),
}

const getPreview = (type, preview, name) => {
    return previews[type] ? previews[type](preview, name) : previews.default(preview, name)
}

export const AttachMediaPreview = ({ selectedFile }) => {

    const { file } = selectedFile
    const { name } = file
    const preview = URL.createObjectURL(file)
    const type = file.type.split('/')[0] || 'file'

    return getPreview(type, preview, name)
}
