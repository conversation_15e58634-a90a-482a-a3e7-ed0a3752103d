import RctCollapsibleCard from 'Components/RctCollapsibleCard/RctCollapsibleCard'
import React from 'react'
import NoPostsFound from 'Routes/components/NoPostsFound'

export const GridList = ({
  items = [],
  build = (item, index) => { },
  refresh = () => { },
  addPostAction = () => { },
  collectionName,
  flexDirection = "row"
}) => {
  if (!items || !items.length) {
    return (
      <RctCollapsibleCard>
        <NoPostsFound refresh={refresh} addPostAction={addPostAction} collection={collectionName} showAdd={false} />
      </RctCollapsibleCard>
    )
  }
  return <div className={flexDirection}>
    {items && items.map((item, index) => {
      let children = build(item, index)
      return React.cloneElement(children, { key: `GridList-Item-${index + 1}` })
    })}
  </div>
}
