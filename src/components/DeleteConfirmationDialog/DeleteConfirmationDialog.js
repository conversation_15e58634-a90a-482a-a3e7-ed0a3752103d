/**
 * Delete Confirmation Dialog
 */
import React, { Component } from 'react'

// lang strings
import { langMessages } from '../../lang'

import Dialog from '@material-ui/core/Dialog'
import DialogActions from '@material-ui/core/DialogActions'
import DialogContent from '@material-ui/core/DialogContent'
import DialogContentText from '@material-ui/core/DialogContentText'
import DialogTitle from '@material-ui/core/DialogTitle'
import Button from '@material-ui/core/Button'

class DeleteConfirmationDialog extends Component {
  state = {
    open: false,
  }

  // open dialog
  open() {
    this.setState({ open: true })
  }

  // close dialog
  close(confirmed) {
    const { onConfirm, onCancel } = this.props

    this.setState({ open: false })

    if (confirmed) {
      onConfirm()
    } else {
      onCancel && onCancel()
    }
  }

  render() {
    const { title, message } = this.props
    return (
      <Dialog open={this.state.open} onClose={() => this.close()} aria-labelledby="alert-dialog-title" aria-describedby="alert-dialog-description">
        <DialogTitle id="alert-dialog-title">{title}</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">{message}</DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => this.close()} className="btn-danger text-white">
            {langMessages['button.cancel']}
          </Button>
          <Button onClick={() => this.close(true)} className="btn-primary text-white" autoFocus>
            {langMessages['button.ok']}
          </Button>
        </DialogActions>
      </Dialog>
    )
  }
}

export default DeleteConfirmationDialog
