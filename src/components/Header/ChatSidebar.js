import React from 'react'
import List from '@material-ui/core/List'
import ListItem from '@material-ui/core/ListItem'
import ListItemText from '@material-ui/core/ListItemText'
import Avatar from '@material-ui/core/Avatar'
import AppBar from '@material-ui/core/AppBar'
import Toolbar from '@material-ui/core/Toolbar'
// import Typography from '@material-ui/core/Typography';

// data
import users from 'Assets/data/chat-app/users'

// helpers
import { textTruncate } from 'Helpers/helpers'

const ChatSidebar = () => (
  <div className="chat-sidebar rct-customizer">
    <AppBar position="static" className="bg-qiplus">
      <Toolbar>
        <div className="d-flex flex-row justify-content-start align-items-center w-100">
          <div className="d-flex flex-column w-75">
            <p className="my-0 py-0"><PERSON><PERSON></p>
            <p className="my-0 py-0">
              <small>+54 11 6802 6916</small>
            </p>
          </div>
          <div className="d-flex justify-content-center align-items-center text-white w-25">
            <i className="fa fa-2x fa-whatsapp" />
          </div>
        </div>
      </Toolbar>
    </AppBar>

    <List>
      {users.map((user, key) => (
        <ListItem key={key} button>
          <Avatar src={user.photo_url} />
          <ListItemText primary={user.firstName + ' ' + user.lastName} secondary={textTruncate(user.last_chat, 16)} />
        </ListItem>
      ))}
    </List>
  </div>
)

export default ChatSidebar
