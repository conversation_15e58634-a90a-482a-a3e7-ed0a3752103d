/**
 * Sidebar Content
 */
import React, { Component } from 'react'
import List from '@material-ui/core/List'
import ListSubheader from '@material-ui/core/ListSubheader'
import { withRouter } from 'react-router-dom'
import { connect } from 'react-redux'

import IntlMessages from 'Util/IntlMessages'

import AgencySidebarMenuItem from './AgencySidebarMenuItem'

// redux actions
import { onToggleAgencyMenu } from 'Actions'

class AgencySidebar extends Component {
  toggleMenu(menu, stateCategory) {
    let data = {
      menu,
      stateCategory,
    }
    this.props.onToggleAgencyMenu(data)
  }

  render() {
    const { agencyNavLinks } = this.props
    return (
      <div className="rct-sidebar-nav">
        <nav className="navigation">
          <List
            className="rct-mainMenu p-0 m-0 list-unstyled"
            subheader={
              <ListSubheader className="side-title" component="li">
                <IntlMessages id="sidebar.general" />
              </ListSubheader>
            }
          >
            {agencyNavLinks.category1.map((menu, key) => (
              <AgencySidebarMenuItem menu={menu} key={key} onToggleAgencyMenu={() => this.toggleMenu(menu, 'category1')} />
            ))}
          </List>
        </nav>
      </div>
    )
  }
}

// map state to props
const mapStateToProps = ({ sidebarReducer: { agencyNavLinks } }) => {
  return { agencyNavLinks }
}

export default withRouter(
  connect(mapStateToProps, {
    onToggleAgencyMenu,
  })(AgencySidebar)
)
