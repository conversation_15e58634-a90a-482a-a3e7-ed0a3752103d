import { Tooltip } from '@material-ui/core'
import React, { useState } from 'react'
import { ChatIcon } from 'Routes/chat/ChatDealAtomic/Atoms'

export const AudioRecorder = ({
  type = 'audio/ogg',
  recordName = 'record',
  audioCodec = 'opus',
  onRecord = (audioFile) => { }
}) => {

  const [permission, setPermission] = useState(false)
  const [mediaRecorder, setMediaRecorder] = useState(null)
  const [chunks, setChunks] = useState([])
  const [duration, setDuration] = useState(0)

  const toggleRecording = () => {
    if (mediaRecorder) {
      mediaRecorder.stop();
      setMediaRecorder(null);
      return;
    }

    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      navigator.mediaDevices
        .getUserMedia({ audio: true })
        .then((stream) => {
          const recorder = new MediaRecorder(stream);
          recorder.audioCodec = audioCodec;

          setMediaRecorder(recorder);

          recorder.addEventListener('start', () => {
            setChunks([]);
            const timestamp = Date.now();
            setDuration(timestamp);
          });

          recorder.addEventListener("stop", () => {

            stream.getTracks().forEach((track) => track.stop());

            const timestamp = Date.now();
            setDuration(timestamp - duration);

            const file = new Blob(chunks, { type, name: recordName, duration });
            onRecord(file);
            setChunks([]);
          });

          recorder.ondataavailable = (e) => {
            chunks.push(e.data);
          };

          recorder.start();
          const stop = () => {
            recorder.stop();
            setMediaRecorder(null);
          };

          return () => stop();
        })
        .catch(console.error);
    } else {
      console.error("getUserMedia not supported on your browser!");
    }
  };


  return (
    <Tooltip title={permission ? 'Record' : 'Permission denied'}>
      <ChatIcon icon={mediaRecorder ? 'stop' : 'mic'} onClick={toggleRecording} />
    </Tooltip>
  )
}
