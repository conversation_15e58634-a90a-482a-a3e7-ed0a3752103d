/**
 * User Block Component
 */
import React, { Component } from 'react'
import { NotificationManager } from 'react-notifications'
import { connect } from 'react-redux'
import { Link } from 'react-router-dom'
import { Dropdown, DropdownMenu, DropdownToggle } from 'reactstrap'

import { ACCOUNTS_COLLECTION_NAME } from '../../constants/AppCollections'
import { ACCOUNT_FIELD, APP_ENV, DEV_ENV, NODE_ENV, PARENT_FIELD, TUTORIALS_URL } from '../../constants/AppConstants'
import { ADMIN_LEVEL, AFFILIATE_ROLE, OWNER_LEVEL } from '../../constants/UsersRoles'

import { clipboardCopy, getAffiliateLink, getParentAccountLink } from 'Helpers/helpers'

// lang strings
import { langMessages } from '../../lang'

// components
import SupportPage from '../Support/Support'

// redux action
import { getOwner, getPosts, getQIUsers, logoutUserFromFirebase, updateAccountOwner } from 'Actions'

// intl messages
import { Avatar, Tooltip } from '@material-ui/core'

import SelectListDialog from 'UIComponents/dialog/SelectListDialog'
import { useAuth } from '../../hooks/auth'
class UserBlock extends Component {
  state = {
    stores: [],
    accountOwner: {},
    relatedAccounts: [],
    userDropdownMenu: false,
    isSupportModal: false,
  }

  componentDidMount() {
    const { user, owner, ownerId, account } = this.props

    if (!owner || !owner.ID || owner.ID !== ownerId) {
      this.props.getOwner(ownerId)
    }

    if (user.level && user.level <= ADMIN_LEVEL) {
      this.props.getPosts(1, ACCOUNTS_COLLECTION_NAME).then(accounts => {
        !accounts.find(a => a.ID === account.ID) && accounts.splice(0, 0, account)
        this.setState({ relatedAccounts: accounts })
      })
    } else if (user.level && user.level <= OWNER_LEVEL) {
      Promise.all([
        this.props.getPosts(1, ACCOUNTS_COLLECTION_NAME, [['ID', '==', user[ACCOUNT_FIELD]]]),
        this.props.getPosts(1, ACCOUNTS_COLLECTION_NAME, [[PARENT_FIELD, '==', user[ACCOUNT_FIELD]]]),
      ]).then(results => {
        let accounts = results.reduce((a, b) => [...a, ...b])
        !accounts.find(a => a.ID === account.ID) && accounts.splice(0, 0, account)
        this.setState({ relatedAccounts: accounts })
      })
    }
  }

  /**
   * Set Owner Account
   */
  setCurrentAccount(accountId) {
    const { relatedAccounts } = this.state
    let newAccount = relatedAccounts.find(a => a.ID === accountId)
    if (newAccount.owner && this.props.ownerId !== newAccount.owner) {
      this.props.updateAccountOwner(newAccount)
      setTimeout(() => document.location.reload(), 500)
    }
  }

  logoutUser() {
    this.props.logoutUserFromFirebase()
  }

  toggleUserDropdown(isOpen) {
    this.setState({ userDropdownMenu: isOpen })
  }

  toggleOwnerDroplist(isOpen) {
    this.setState({ ownerDialogOpen: isOpen, userDropdownMenu: isOpen })
  }

  openSupportModal() {
    this.setState({ isSupportModal: true })
  }

  onCloseSupportPage() {
    this.setState({ isSupportModal: false })
  }

  onSubmitSupport() {
    this.setState({ isSupportModal: false })
    NotificationManager.success('Message has been sent successfully!')
  }

  render() {
    const {
      user,
      account,
      account: { active },
      settings: { darkMode },
      redirectToCheckout
    } = this.props
    const { relatedAccounts } = this.state

    return (
      <div className="sidebar-user">
        <div className="sidebar-user-block">
          {(APP_ENV === DEV_ENV || APP_ENV !== NODE_ENV) && (
            <small
              className="bg-qiplus d-block font-xs line-13 position-absolute text-center text-white top-0 w-100 z-10"
              style={{ textOverflow: 'ellipsis', maxWidth: '100%' }}
            >
              {APP_ENV}
            </small>
          )}
          {user.accountId === account.ID && active !== true ? (
            <div className="session-account-title bg-qiplus text-white font-xs line-13 nowrap">
              <small>{langMessages['accounts.inactiveAccount']}</small>
            </div>
          ) : (
            user.accountId !== account.ID && (
              <Tooltip title={`${account.title} - ${langMessages[`statuses.${active ? 'active' : 'inactive'}.fem`]}`} placement="top-start">
                <div className="session-account-title bg-qiplus text-white font-xs line-13 nowrap">
                  {active !== true ? (
                    <span>
                      <small>{(account.title || '').substr(0, 3)}</small>
                      <b className="bg-youtube px-10 ml-1 float-right">{langMessages['accounts.inactiveAccount']}</b>
                    </span>
                  ) : (
                    <small>{account.title}</small>
                  )}
                </div>
              </Tooltip>
            )
          )}
          <Dropdown
            isOpen={this.state.ownerDialogOpen || this.state.userDropdownMenu}
            toggle={() => this.toggleUserDropdown(this.state.ownerDialogOpen || !this.state.userDropdownMenu)}
            className="rct-dropdown"
          >
            <DropdownToggle tag="div" className="d-flex align-items-center">
              <div className="user-profile">
                {(user.avatar && user.avatar.indexOf('http') >= 0 && (
                  <img src={user.avatar} alt="user profile" className="img-fluid rounded-circle" width="50" height="100" />
                )) || (
                    <Avatar title={user.displayName || user.firstName} className="rounded-circle bg-qiplus mb-5 mr-5">
                      {(user.displayName || user.firstName || '').charAt(0).toUpperCase()}
                    </Avatar>
                  )}
              </div>
              <div className="user-info">
                <span className="user-name ml-4">{user.firstName || user.displayName}</span>
                <i className="zmdi zmdi-chevron-down dropdown-icon mx-4"></i>
              </div>
            </DropdownToggle>
            <DropdownMenu>
              <ul className="list-unstyled mb-0">
                <li className="p-15 border-bottom user-profile-top bg-qiplus rounded-top">
                  <p className="text-white mb-0 fs-14">{user.displayName || user.firstName}</p>
                  <span className="text-white fs-14">{user.email || ''}</span>
                </li>
                {!!account && (
                  <li className={`${darkMode ? '' : 'border-top'}`}>
                    <a className="position-relative" onClick={e => relatedAccounts.length > 1 && this.toggleOwnerDroplist(true)}>
                      <i className="ti-id-badge text-dark mr-3"></i>
                      <span className="text-dark">{account.title}</span>
                      {relatedAccounts.length > 1 && <i className="fa fa-chevron-circle-down position-absolute top-15 right-5 text-base"></i>}
                    </a>
                  </li>
                )}
                {user.level && user.level <= OWNER_LEVEL && !account.parentId && (
                  <Tooltip title={langMessages['texts.copyParentLink']} placement="top">
                    <li className={`${darkMode ? '' : 'border-top'}`}>
                      <a
                        onClick={e => {
                          e.preventDefault()
                          var href = getParentAccountLink(account)
                          clipboardCopy(href, () => {
                            NotificationManager.success(langMessages['texts.copiedToClipboard'])
                          })
                        }}
                      >
                        <i className="fa fa-clone mr-3"></i>
                        <span>{langMessages['texts.parentLink']}</span>
                      </a>
                    </li>
                  </Tooltip>
                )}
                {user.roles && user.roles.includes(AFFILIATE_ROLE) && (
                  <Tooltip title={langMessages['texts.copyToClipboard']} placement="top">
                    <li className={`${darkMode ? '' : 'border-top'}`}>
                      <a
                        onClick={e => {
                          e.preventDefault()
                          var href = getAffiliateLink(user)
                          clipboardCopy(href, () => {
                            NotificationManager.success(langMessages['texts.copiedToClipboard'])
                          })
                        }}
                      >
                        <i className="fa fa-clone mr-3"></i>
                        <span>{langMessages['texts.affiliateLink']}</span>
                      </a>
                    </li>
                  </Tooltip>
                )}
                {user.level && user.level <= OWNER_LEVEL && user.accountId === account.ID && (
                  <li className={`${darkMode ? '' : 'border-top'}`}>
                    <a
                      onClick={redirectToCheckout}
                    >
                      <i className="icon-rocket text-dark mr-3"></i>
                      <span>{langMessages['widgets.upgradePlan']}</span>
                    </a>
                  </li>
                )}
                {user.level && user.level <= OWNER_LEVEL && user.accountId === account.ID && (
                  <li className={`${darkMode ? '' : 'border-top'}`}>
                    <Link
                      to={{
                        pathname: '/settings',
                      }}
                    >
                      <i className="fa fa-cogs text-dark mr-3"></i>
                      <span>{langMessages['accounts.settings']}</span>
                    </Link>
                  </li>
                )}
                <li className={`${darkMode ? '' : 'border-top'}`}>
                  <Link
                    to={{
                      pathname: '/profile/',
                      state: { activeTab: 0 },
                    }}
                  >
                    <i className="icon-user text-dark mr-3"></i>
                    <span>{langMessages['widgets.myProfile']}</span>
                  </Link>
                </li>
                <li className={`${darkMode ? '' : 'border-top'}`}>
                  <a href={TUTORIALS_URL} target="_blank" rel="noreferrer">
                    <i className="icon-graduation text-dark mr-3"></i>
                    <span>{langMessages['widgets.tutorials']}</span>
                  </a>
                </li>
                {/* <li>
									<Link to={{
										pathname: '/users/user-profile-1',
										state: { activeTab: 2 }
									}}>
										<i className="zmdi zmdi-comment-text-alt text-success mr-3"></i>
										<span>{langMessages["widgets.messages"]}</span>
										<Badge color="danger" className="pull-right">3</Badge>
									</Link>
								</li>
								<li>
									<Link to="/pages/feedback">
										<i className="zmdi zmdi-edit text-warning mr-3"></i>
										<span>{langMessages["sidebar.feedback"]}</span>
										<Badge color="info" className="pull-right">1</Badge>
									</Link>
								</li> */}
                <li className={`${darkMode ? '' : 'border-top'}`}>
                  <a onClick={e => this.logoutUser()}>
                    <i className="zmdi zmdi-power text-danger mr-3"></i>
                    <span>{langMessages['widgets.logOut']}</span>
                  </a>
                </li>
              </ul>
            </DropdownMenu>
          </Dropdown>
          <SelectListDialog
            withAvatar
            button={false}
            isOpen={this.state.ownerDialogOpen}
            width={400}
            variant="outlined"
            value={this.props.ownerId}
            dialogTitle={langMessages['texts.selectOwner']}
            buttonLabel={langMessages['texts.selectOwner']}
            onChange={value => this.setCurrentAccount(value)}
            onClick={e => this.toggleOwnerDroplist(true)}
            onClose={e => this.toggleOwnerDroplist(false)}
            choices={this.state.relatedAccounts
              .sort((a, b) => (a.title === b.title ? 0 : a.title > b.title ? 1 : -1))
              .map((a, k) => ({
                value: a.ID,
                label: a.title,
                avatar: '',
                icon: '',
              }))}
          />
        </div>
        <SupportPage
          isOpen={this.state.isSupportModal}
          onCloseSupportPage={() => this.onCloseSupportPage()}
          onSubmit={() => this.onSubmitSupport()}
        />
      </div>
    )
  }
}

// map state to props
const mapStateToProps = ({ settings, authReducer, ownersReducer, qiusersReducer }) => {
  const { owner, owners } = ownersReducer
  const { qiusers } = qiusersReducer
  const { user, ownerId, account } = authReducer
  return { settings, user, qiusers, owner, owners, ownerId, account }
}

const mapDispatchToProps = {
  logoutUserFromFirebase,
  updateAccountOwner,
  getQIUsers,
  getPosts,
  getOwner,
}

// Create a wrapper component that uses the hook and passes it to UserBlock
const UserBlockWithAuth = (props) => {
  const { redirectToCheckout } = useAuth()
  return <UserBlock {...props} redirectToCheckout={redirectToCheckout} />
}

export default connect(mapStateToProps, mapDispatchToProps)(UserBlockWithAuth)
