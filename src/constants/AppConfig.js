/**
 * App Config File
 */

import { APP_ENV } from './AppConstants';
import themeColors from './ThemeColors';

let enableUserTour = APP_ENV === 'production' ? true : false

// Detect browser language and use it if supported, otherwise fallback to English
const getBrowserLanguage = () => {
  const browserLang = navigator?.language?.split('-')?.[0] || 'en';
  console.log('BROWSER', browserLang)
  // Only accept pt, en, or es languages
  const locale = ['pt', 'en', 'es'].includes(browserLang) ? browserLang : 'en';
  console.log('LOC', locale)
  return locale
};

const userLocale = localStorage.getItem('config.locale') || getBrowserLanguage()

const languages = {
  en: {
    languageId: 'english',
    locale: 'en',
    subLocale: 'en_US',
    name: 'English',
    icon: 'en',
  },
  pt: {
    languageId: 'portuguese',
    locale: 'pt',
    subLocale: 'pt_BR',
    name: 'Portuguese',
    icon: 'pt',
  },
  es: {
    languageId: 'spanish',
    locale: 'es',
    subLocale: 'es_ES',
    name: 'Spanish',
    icon: 'es',
  },
}

const AppConfig = {
  appLogo: require('Assets/img/site-logo.png'), // App Logo
  altLogo: require('Assets/img/logo-alt.png'), // App Logo
  // colorLogo: require('Assets/img/invoice-logo.png'),                // App Logo
  brandName: 'QI Plus', // Brand Name
  navCollapsed: false, // Sidebar collapse
  boxLayout: false, // Box Layout
  rtlLayout: false, // RTL Layout
  darkMode: localStorage.getItem('config.darkMode'), // Dark Mode
  isDarkSidenav: true, // Set true to dark sidebar
  miniSidebar: !localStorage.getItem('config.miniSidebar'), // Mini Sidebar
  enableSidebarBackgroundImage: false, // Enable Sidebar Background Image
  sidebarImage: require('Assets/img/sidebar-qi.jpg'), // Select sidebar image
  enableThemeOptions: false, // Enable Theme Options
  locale: languages[userLocale] || languages['en'],
  enableUserTour, // Enable / Disable User Tour
  copyRightText: 'QIPlus © 2019', // Copy Right Text
  // light theme colors
  themeColors,
  // dark theme colors
  darkThemeColors: {
    darkBgColor: '#424242',
  },
}

export default AppConfig
