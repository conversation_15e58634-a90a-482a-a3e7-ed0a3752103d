// lang strings
import { CUSTOM_FIELDS_GROUP } from 'Constants'
import { langMessages } from '../lang'

export const CustomFieldObject = {
  ID: '',
  label: langMessages['forms.customField'],
  name: 'custom_field',
  type: 'repeater',
  group: CUSTOM_FIELDS_GROUP,
  instructions: '',
  required: 0,
  conditional_logic: 0,
  wrapper: {
    width: '',
    class: '',
    id: '',
  },
  collapsed: '',
  min: 0,
  max: 0,
  layout: 'block',
  button_label: langMessages['actions.addField'],
  sub_fields: [
    {
      label: langMessages['forms.fieldType'],
      name: 'type',
      type: 'select',
      instructions: '',
      required: 0,
      conditional_logic: 0,
      wrapper: {
        width: '50',
        class: '',
        id: '',
      },
      choices: {
        text: langMessages['fieldTypes.text'],
        textarea: langMessages['fieldTypes.textarea'],
        email: langMessages['fieldTypes.email'],
        password: langMessages['fieldTypes.password'],
        number: langMessages['fieldTypes.number'],
        date_picker: langMessages['fieldTypes.date_picker'],
        url: langMessages['fieldTypes.url'],
        select: langMessages['fieldTypes.select'],
        radio: langMessages['fieldTypes.radio'],
        checkbox: langMessages['fieldTypes.checkbox'],
      },
      default_value: [],
      allow_null: 0,
      multiple: 0,
      ui: 0,
      return_format: 'value',
      ajax: 0,
      placeholder: '',
    },
    {
      label: langMessages['forms.fieldName'],
      name: 'name',
      type: 'text',
      instructions: 'Não pode conter espaços nem caracteres especiais',
      required: 0,
      conditional_logic: 0,
      wrapper: {
        width: '50',
        class: '',
        id: '',
      },
      default_value: '',
      placeholder: '',
      prepend: '',
      append: '',
      maxlength: '',
    },
    {
      label: langMessages['forms.fieldLabel'],
      name: 'label',
      type: 'text',
      instructions: 'O rótulo do campo visível no formulário',
      required: 0,
      conditional_logic: 0,
      wrapper: {
        width: '50',
        class: '',
        id: '',
      },
      default_value: '',
      placeholder: '',
      prepend: '',
      append: '',
      maxlength: '',
    },
    {
      label: langMessages['forms.required'],
      name: 'required',
      type: 'boolean',
      instructions: '',
      required: 0,
      conditional_logic: 0,
      wrapper: {
        width: '50',
        class: '',
        id: '',
      },
      message: '',
      default_value: 0,
      ui: 1,
      ui_on_text: '',
      ui_off_text: '',
    },
  ],
}

export default CustomFieldObject
