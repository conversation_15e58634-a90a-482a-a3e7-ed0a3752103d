/**
 * English language constants for modules
 */

const EnModules = {
  accounts: {
    label: 'Accounts',
    singular: 'Account',
    shortname: 'Account',
  },
  affiliates: {
    label: 'Affiliates',
    singular: 'Affiliate',
    shortname: 'Affiliate',
  },
  automations: {
    label: 'Automations',
    singular: 'Automation',
    shortname: 'Automation',
  },
  calendar: {
    label: 'Calendar',
    singular: 'Event',
    shortname: 'Event',
  },
  campaigns: {
    label: 'Campaigns',
    singular: 'Campaign',
    shortname: 'Campaign',
  },
  chats: {
    label: 'Chat',
    singular: 'Chat',
    shortname: 'Chat',
  },
  contracts: {
    label: 'Contracts',
    singular: 'Contract',
    shortname: 'Contract',
  },
  customFields: {
    label: 'Custom Fields',
    singular: 'Custom Field',
    shortname: 'Fields',
  },
  deals: {
    label: 'Deals',
    singular: 'Deal',
    shortname: 'Deal',
  },
  events: {
    label: 'Events',
    singular: 'Event',
    shortname: 'Event',
  },
  fields: {
    label: 'Fields',
    singular: 'Field',
    shortname: 'Field',
  },
  forms: {
    label: 'Forms',
    singular: 'Form',
    shortname: 'Form',
  },
  funnels: {
    label: 'Sales Funnels',
    singular: 'Sales Funnel',
    shortname: 'Funnel',
  },
  individualPlans: {
    label: 'Individual Plans',
    singular: 'Individual Plan',
    shortname: 'Individual Plan',
  },
  integrations: {
    label: 'Integrations',
    singular: 'Integration',
    shortname: 'Integration',
  },
  landingPages: {
    label: 'Landing Pages',
    singular: 'Landing Page',
    shortname: 'Landing Page',
  },
  leads: {
    label: 'Leads',
    singular: 'Lead',
    shortname: 'Lead',
  },
  mail: {
    label: 'Emails',
    singular: 'Email',
    shortname: 'Email',
  },
  mailboxes: {
    label: 'Inboxes',
    singular: 'Inbox',
    shortname: 'Inbox',
  },
  mailing: {
    label: 'Emails',
    singular: 'Email',
    shortname: 'Email',
  },
  notifications: {
    label: 'Notifications',
    singular: 'Notification',
    shortname: 'Notification',
  },
  products: {
    label: 'Products',
    singular: 'Product',
    shortname: 'Product',
  },
  'qiplus-plans': {
    label: 'QIPlus plans',
    singular: 'QIPlus plan',
    shortname: 'Plan',
  },
  qiusers: {
    label: 'Users',
    singular: 'User',
    shortname: 'User',
  },
  questionnaires: {
    label: 'Questionnaires',
    singular: 'Questionnaire',
    shortname: 'Questionnaire',
  },
  segmentations: {
    label: 'Segmentations',
    singular: 'Segmentation',
    shortname: 'Segmentation',
  },
  stores: {
    label: 'Stores',
    singular: 'Store',
    shortname: 'Store',
  },
  tasklists: {
    label: 'Checklists',
    singular: 'Checklist',
    shortname: 'Checklist',
  },
  teams: {
    label: 'Teams',
    singular: 'Team',
    shortname: 'Team',
  },
  tickets: {
    label: 'Tickets',
    singular: 'Ticket',
    shortname: 'Ticket',
  },
  trackingsLeads: {
    label: 'Lead Tracking',
    singular: 'Lead Tracking',
    shortname: 'Tracking',
  },
  users: {
    label: 'Users',
    singular: 'User',
    shortname: 'User',
  },
  // Add more modules as needed
};

export default EnModules;
