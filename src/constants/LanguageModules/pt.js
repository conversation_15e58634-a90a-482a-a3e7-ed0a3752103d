/**
 * Portuguese language constants for modules
 */

const PtModules = {
  accounts: {
    label: '<PERSON><PERSON>',
    singular: '<PERSON><PERSON>',
    shortname: '<PERSON><PERSON>',
  },
  affiliates: {
    label: 'Afiliados',
    singular: '<PERSON>filia<PERSON>',
    shortname: '<PERSON><PERSON><PERSON><PERSON>',
  },
  automations: {
    label: 'Automações',
    singular: '<PERSON>ma<PERSON>',
    shortname: 'Automa<PERSON>',
  },
  calendar: {
    label: 'Calendário',
    singular: 'Event<PERSON>',
    shortname: 'Evento',
  },
  campaigns: {
    label: 'Campan<PERSON>',
    singular: 'Campan<PERSON>',
    shortname: 'Campan<PERSON>',
  },
  chats: {
    label: 'Chat',
    singular: 'Chat',
    shortname: 'Chat',
  },
  contracts: {
    label: '<PERSON>tra<PERSON>',
    singular: '<PERSON>tra<PERSON>',
    shortname: '<PERSON>tra<PERSON>',
  },
  customFields: {
    label: 'Campos Personalizados',
    singular: 'Campo Personalizado',
    shortname: 'Campos',
  },
  deals: {
    label: 'Negócios',
    singular: 'Negócio',
    shortname: '<PERSON>eg<PERSON><PERSON>',
  },
  events: {
    label: 'Eventos',
    singular: '<PERSON><PERSON>',
    shortname: '<PERSON><PERSON>',
  },
  fields: {
    label: 'Campos',
    singular: '<PERSON>',
    shortname: '<PERSON>',
  },
  forms: {
    label: 'Formulários',
    singular: '<PERSON>ul<PERSON><PERSON>',
    shortname: 'Formulário',
  },
  funnels: {
    label: 'Funis de Vendas',
    singular: 'Funil de Vendas',
    shortname: 'Funil',
  },
  individualPlans: {
    label: 'Planos Avulsos',
    singular: 'Plano Avulso',
    shortname: 'Plano Avulso',
  },
  integrations: {
    label: 'Integrações',
    singular: 'Integração',
    shortname: 'Integração',
  },
  landingPages: {
    label: 'Páginas de Destino',
    singular: 'Página de Destino',
    shortname: 'Página',
  },
  leads: {
    label: 'Leads',
    singular: 'Lead',
    shortname: 'Lead',
  },
  mail: {
    label: 'Emails',
    singular: 'Email',
    shortname: 'Email',
  },
  mailboxes: {
    label: 'Caixas de Entrada',
    singular: 'Caixa de Entrada',
    shortname: 'Inbox',
  },
  mailing: {
    label: 'Emails',
    singular: 'Email',
    shortname: 'Email',
  },
  notifications: {
    label: 'Notificações',
    singular: 'Notificação',
    shortname: 'Notificação',
  },
  products: {
    label: 'Produtos',
    singular: 'Produto',
    shortname: 'Produto',
  },
  'qiplus-plans': {
    label: 'Planos QIPlus',
    singular: 'Plano QIPlus',
    shortname: 'Plano',
  },
  qiusers: {
    label: 'Usuários',
    singular: 'Usuário',
    shortname: 'Usuário',
  },
  questionnaires: {
    label: 'Questionários',
    singular: 'Questionário',
    shortname: 'Questionário',
  },
  segmentations: {
    label: 'Segmentações',
    singular: 'Segmentação',
    shortname: 'Segmentação',
  },
  stores: {
    label: 'Lojas',
    singular: 'Loja',
    shortname: 'Loja',
  },
  tasklists: {
    label: 'Checklists',
    singular: 'Checklist',
    shortname: 'Checklist',
  },
  teams: {
    label: 'Equipes',
    singular: 'Equipe',
    shortname: 'Equipe',
  },
  tickets: {
    label: 'Pedidos',
    singular: 'Pedido',
    shortname: 'Pedido',
  },
  trackingsLeads: {
    label: 'Rastreamento de Leads',
    singular: 'Rastreamento de Leads',
    shortname: 'Rastreamento',
  },
  users: {
    label: 'Usuários',
    singular: 'Usuário',
    shortname: 'Usuário',
  },

  // Add more modules as needed
};

export default PtModules;
