/**
 * Managers Actions
 */
import { FirestoreRef } from 'FirebaseRef'

import { GET_MANAGERS, GET_MANAGERS_SUCCESS, GET_MANAGERS_FAILURE } from 'Actions/types'

import { ACCOUNT_FIELD, ROLES_FIELD, MANAGER_FIELD } from '../constants/AppConstants'
import COLLECTIONS from '../constants/AppCollections'

const collectionName = COLLECTIONS.QIUSERS_COLLECTION_NAME

/**
 * Redux Action Get Managers
 */
export const getManagers = (accountId, queries) => (dispatch, getState) => {
  dispatch({ type: GET_MANAGERS })
  return new Promise((resolve, reject) => {
    let QueryRef = FirestoreRef.collection(collectionName)
      .where(ACCOUNT_FIELD, '==', `${accountId}`)
      .where(ROLES_FIELD, 'array-contains', MANAGER_FIELD)

    Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))
    QueryRef.get()
      .then(snapshot => {
        const posts = []
        snapshot.forEach(doc => {
          posts.push(doc.data())
        })
        dispatch({ type: GET_MANAGERS_SUCCESS, payload: posts })
        return resolve(posts)
      })
      .catch(function (error) {
        dispatch({ type: GET_MANAGERS_FAILURE })
      })
  }).catch(error => console.error(error) || dispatch({ type: GET_MANAGERS_FAILURE }))
}
