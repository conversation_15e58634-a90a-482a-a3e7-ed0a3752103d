/**
 * Chat App Actions
 */
import { FirestoreRef } from 'FirebaseRef'

import {
  CHAT_WITH_SELECTED_USER,
  SEND_MESSAGE_TO_USER,
  UPDATE_USERS_SEARCH,
  <PERSON>AR<PERSON>_USERS,
  ADD_CHAT_USER,
  ADD_NEW_CHAT_USER,
  ADD_NEW_CHAT_USER_SUCCESS,
  ADD_NEW_CHAT_USER_ERROR,
  ADD_CHAT_GROUP,
  ADD_CHAT_GROUP_SUCCESS,
  ADD_CHAT_GROUP_ERROR,
  LEAVE_CHAT,
  LEAVE_CHAT_SUCCESS,
  LEAVE_CHAT_ERROR,
  <PERSON>EAVE_GRO<PERSON>,
  <PERSON>EA<PERSON>_<PERSON><PERSON><PERSON>_SUCCESS,
  LEAVE_GROUP_ERROR,
  <PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_SUCCESS,
  DELETE_GROUP_ERROR,
  GET_CHAT_USERS,
  GET_CHAT_USERS_ERROR,
  GET_CHAT_USERS_SUCCESS,
  GET_CHAT_MESSAGES,
  GET_CHAT_MESSAGES_ERROR,
  GET_CHAT_MESSAGES_SUCCESS,
  AD<PERSON>_CHAT_MESSAGE,
  ADD_CHAT_MESSAGE_ERROR,
  ADD_CHAT_MESSAGE_SUCCESS,
  FIRESTORE_ERROR,
  GET_GROUPS_FAILURE,
  GET_GROUPS_SUCCESS,
  GET_GROUPS_KEYWORDS,
  GET_GROUPS,
  GET_WP_MESSAGES_SUCCESS,
  SET_CHAT_ACCOUNT,
} from './types'

import { CHATS_COLLECTION_NAME, MESSAGES_SUBCOLLECTION_NAME } from '../constants/AppCollections'
import { CREATED_FIELD, UPDATED_FIELD } from '../constants/AppConstants'
import { createGroup } from '../routes/chat/model/createGroup'
import { createChat } from '../routes/chat/model/createChat'
import { isLoopableObject, toSearchKeyword } from '../helpers/helpers'

const collectionName = CHATS_COLLECTION_NAME

/**
 * Redux Action To Emit Boxed Layout
 * @param {*boolean} isBoxLayout
 */
export const chatWithSelectedUser = user => ({
  type: CHAT_WITH_SELECTED_USER,
  payload: user,
})

export const sendMessageToUser = data => ({
  type: SEND_MESSAGE_TO_USER,
  payload: data,
})

/**
 * Redux Action To Update User Search
 */
export const updateUsersSearch = value => ({
  type: UPDATE_USERS_SEARCH,
  payload: value,
})

/**
 * export const to search users
 */
export const onSearchUsers = value => ({
  type: SEARCH_USERS,
  payload: value,
})

/**
 * Listen Chat
 */
export const listenChatUsers = (qiuserId, callback) => (dispatch, getState) => {
  const {
    authReducer: { user },
  } = getState()
  qiuserId = qiuserId || user.ID
  dispatch({ type: GET_CHAT_USERS })

  return new Promise(res => {
    FirestoreRef.collection(collectionName)
      .where('users', 'array-contains', qiuserId)
      .orderBy(UPDATED_FIELD, 'desc')
      .limit(50)
      .onSnapshot(
        ({ docs }) => {
          // append new docs to message array
          const chats = docs.map(doc => doc.data())
          // console.log('listenChatUsers > chats',chats);
          dispatch({
            type: GET_CHAT_USERS_SUCCESS,
            payload: chats,
          })
          callback && callback(chats)
          return res(chats)
        },
        err => {
          console.log('listenChatUsers > err', err)
          dispatch({
            type: GET_CHAT_USERS_ERROR,
          })
        }
      )
  })
}

export const setChatAccount = chatAccount => (dispatch, getState) => {
  dispatch({
    type: SET_CHAT_ACCOUNT,
    payload: chatAccount,
  })
}

export const setChatMessages =
  ({ messages, chatId }) =>
  (dispatch, getState) => {
    dispatch({
      type: GET_WP_MESSAGES_SUCCESS,
      chatId,
      payload: messages,
    })
  }

/**
 * Add Chat
 */
export const addChat = ({ contact, user, source }) => {
  const chatData = createChat({ contact, user, source })
  return {
    type: ADD_CHAT_USER,
    payload: chatData,
  }
}

/**
 * Add New Chat
 */
export const createNewChat =
  ({ contact, user, source }) =>
  (dispatch, getState) => {
    const chatData = createChat({ contact, user, source })
    const chatId = chatData.ID
    dispatch({ type: ADD_NEW_CHAT_USER, chatId })

    return new Promise(res => {
      const chatRef = FirestoreRef.collection(collectionName).doc(chatId)

      chatRef.get().then(doc => {
        if (!doc.exists || !doc.data().ID) {
          chatRef
            .set(chatData)
            .then(result => {
              // console.log({result});
              dispatch({
                type: ADD_NEW_CHAT_USER_SUCCESS,
                chatId,
                payload: chatData,
              })
              addChat({ contact, user, source })
              res(chatData)
            })
            .catch(err => {
              console.error(err)
              dispatch({
                type: ADD_NEW_CHAT_USER_ERROR,
                chatId,
                error: err,
              })
              res({ error: err })
            })
        } else if (doc.data().ID) {
          const { users } = doc.data()
          if (Array.isArray(users) && users.includes(user.ID)) {
            return
          }
          const newChatData = {
            ...chatData,
            users: [...new Set([...(users || []), user.ID])],
          }
          chatRef
            .update({
              users: newChatData.users,
            })
            .then(result => {
              // console.log({result});
              dispatch({
                type: ADD_NEW_CHAT_USER_SUCCESS,
                chatId,
                payload: newChatData,
              })
              addChat({ contact, user, source })
              res(newChatData)
            })
            .catch(err => {
              console.error(err)
              dispatch({
                type: ADD_NEW_CHAT_USER_ERROR,
                chatId,
                error: err,
              })
              res({ error: err })
            })
        }
      })
    })
  }

/**
 * Add Group
 */
export const addGroup = ({ avatar, user, source, userIds, groupName }) => {
  const groupData = createGroup({ avatar, user, source, userIds, groupName })
  return {
    type: ADD_CHAT_USER,
    payload: groupData,
  }
}

/**
 * Add New Group
 */
export const createNewGroup =
  ({ avatar, user, source, userIds, groupName }) =>
  (dispatch, getState) => {
    const groupData = createGroup({ avatar, user, source, userIds, groupName })
    dispatch({ type: ADD_CHAT_GROUP })
    return new Promise(res => {
      FirestoreRef.collection(collectionName)
        .add(groupData)
        .then(({ id }) => {
          const chatId = id
          const newGroupData = {
            ...groupData,
            id,
            ID: id,
          }
          dispatch({
            type: ADD_CHAT_GROUP_SUCCESS,
            chatId,
            payload: newGroupData,
          })
          res(newGroupData)
        })
        .catch(err => {
          console.error(err)
          dispatch({
            type: ADD_CHAT_GROUP_ERROR,
            error: err,
          })
          res({ error: err })
        })
    })
  }

/**
 * Add New Group
 */
export const deleteGroup = (groupData, qiuserId) => (dispatch, getState) => {
  const {
    authReducer: { user },
  } = getState()
  qiuserId = qiuserId || user.ID

  dispatch({ type: DELETE_GROUP, chatId: groupData.ID })
  return new Promise(res => {
    FirestoreRef.collection(collectionName)
      .doc(groupData.ID)
      .delete()
      .then(() => {
        dispatch({
          type: DELETE_GROUP_SUCCESS,
          chatId: groupData.ID,
        })
        res(groupData)
      })
      .catch(err => {
        console.error(err)
        dispatch({
          type: DELETE_GROUP_ERROR,
          error: err,
        })
        res({ error: err })
      })
  })
}

/**
 * Leave Chat
 */
export const leaveChat = (chatData, qiuserId) => (dispatch, getState) => {
  const {
    authReducer: { user },
  } = getState()
  qiuserId = qiuserId || user.ID

  dispatch({ type: LEAVE_CHAT, chatId: chatData.ID })
  return new Promise(res => {
    const users = chatData.users.filter(uId => uId !== qiuserId)
    const newChatData = {
      ...chatData,
      users,
    }
    FirestoreRef.collection(collectionName)
      .doc(chatData.ID)
      .update({ users })
      .then(() => {
        dispatch({
          type: LEAVE_CHAT_SUCCESS,
          chatId: chatData.ID,
          payload: newChatData,
        })
        res(newChatData)
      })
      .catch(err => {
        console.error(err)
        dispatch({
          type: LEAVE_CHAT_ERROR,
          error: err,
        })
        res({ error: err })
      })
  })
}
/**
 * Leave Group
 */
export const leaveGroup = (groupData, qiuserId) => (dispatch, getState) => {
  const {
    authReducer: { user },
  } = getState()
  qiuserId = qiuserId || user.ID

  dispatch({ type: LEAVE_GROUP, chatId: groupData.ID })
  return new Promise(res => {
    const users = groupData.users.filter(uId => uId !== qiuserId)
    const admins = groupData.admins.filter(uId => uId !== qiuserId)
    if (!users.length) {
      return dispatch(deleteGroup(groupData, qiuserId))
    }
    if (!admins.length) {
      admins.push(users[0])
    }
    const newGroupData = {
      ...groupData,
      users,
      admins,
    }
    FirestoreRef.collection(collectionName)
      .doc(groupData.ID)
      .update({ users, admins })
      .then(() => {
        dispatch({
          type: LEAVE_GROUP_SUCCESS,
          chatId: groupData.ID,
          payload: newGroupData,
        })
        res(newGroupData)
      })
      .catch(err => {
        console.error(err)
        dispatch({
          type: LEAVE_GROUP_ERROR,
          error: err,
        })
        res({ error: err })
      })
  })
}

/**
 * Redux Action Get Groups
 */
export const getGroups = (accountId, where, params) => (dispatch, getState) => {
  const {
    authReducer: { account },
  } = getState()
  accountId = accountId || account.ID

  dispatch({ type: GET_GROUPS, collection: collectionName })

  return new Promise((resolve, reject) => {
    const queries = Array.isArray(where) ? where : []
    const qObj = isLoopableObject(where) ? where : params || {}

    let QueryRef = FirestoreRef.collection(collectionName)
    if (accountId !== 1) {
      QueryRef = QueryRef.where('accountId', '==', `${accountId}`)
    }

    Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))

    if (qObj.searchTerm) {
      const keyword = toSearchKeyword(qObj.searchTerm)
      QueryRef = QueryRef.where('keywords', 'array-contains', `${keyword}`)

      if (!queries.length) {
        const keywords = [keyword]
        dispatch({ type: GET_GROUPS_KEYWORDS, keywords })
      }
    } else if (Array.isArray(qObj.keywords)) {
      const keywords = qObj.keywords.map(w => toSearchKeyword(w))
      QueryRef = QueryRef.where('keywords', 'array-contains-any', keywords)

      if (!queries.length) {
        dispatch({ type: GET_GROUPS_KEYWORDS, keywords })
      }
    } else {
      if (qObj.orderBy) QueryRef = QueryRef.orderBy(qObj.orderBy)
    }

    if (qObj.limit) QueryRef = QueryRef.limit(qObj.limit)

    QueryRef.get()
      .then(snapshot => {
        const leads = []
        snapshot.forEach(doc => {
          leads.push(doc.data())
        })
        dispatch({ type: GET_GROUPS_SUCCESS, collection: collectionName, payload: leads })
        return resolve(leads)
      })
      .catch(function (error) {
        console.error(error)
        dispatch({ type: GET_GROUPS_FAILURE, collection: collectionName })
        dispatch({ type: FIRESTORE_ERROR, error })
        return reject(error)
      })
  })
}

/**
 *  Paginate Messages
 *  TIREI DE MEDIUM ESTE EXEMPLO
 *  https://medium.com/@650egor/firestore-reactive-pagination-db3afb0bf42e
 */

const messages = {}
const listeners = {}
const start = {}
const end = {}

export const listenChatMessages = (chatId, isGroup, qiuserId) => (dispatch, getState) => {
  const {
    authReducer: { user },
  } = getState()
  qiuserId = qiuserId || user.ID

  if (start[chatId]) return

  dispatch({ type: GET_CHAT_MESSAGES, chatId })

  messages[chatId] = []
  listeners[chatId] = []
  start[chatId] = null
  end[chatId] = null

  // query reference for the messages we want
  let chatRef = FirestoreRef.collection(collectionName).doc(chatId).collection(MESSAGES_SUBCOLLECTION_NAME)

  if (!isGroup) {
    chatRef = chatRef.where('from', '==', qiuserId)
  }

  // single query to get startAt snapshot
  let listener = chatRef
    .orderBy(CREATED_FIELD, 'desc')
    .limit(50)
    // create listener using startAt snapshot (starting boundary)
    .onSnapshot(
      ({ docs }) => {
        const payload = docs.map(doc => doc.data())
        // console.log('listenChatMessages > docs',{chatId, isGroup, qiuserId, payload});
        // append new docs to message array
        dispatch({
          type: GET_CHAT_MESSAGES_SUCCESS,
          chatId,
          payload,
        })
        // add listener to list
        listeners[chatId].push(listener)
      },
      err => {
        console.log('listenChatMessages > err', err)
        dispatch({
          type: GET_CHAT_MESSAGES_ERROR,
        })
      }
    )
}

export const listenMyChatMessages = qiuserId => (dispatch, getState) => {
  const {
    authReducer: { user },
  } = getState()
  qiuserId = qiuserId || user.ID

  if (start[qiuserId]) return

  dispatch({ type: GET_CHAT_MESSAGES, qiuserId })

  messages[qiuserId] = []
  listeners[qiuserId] = []
  start[qiuserId] = null
  end[qiuserId] = null

  // query reference for the messages we want
  let chatRef = FirestoreRef.collection(collectionName).doc(qiuserId)
  let messagesRef = chatRef.collection(MESSAGES_SUBCOLLECTION_NAME)

  // single query to get startAt snapshot
  let listener = messagesRef
    .orderBy(CREATED_FIELD, 'desc')
    .limit(500)
    // create listener using startAt snapshot (starting boundary)
    .onSnapshot(
      async ({ docs }) => {
        // append new docs to message array
        const msgs = docs.map(doc => doc.data())
        const chatMessages = msgs.reduce((a, b) => {
          return {
            ...a,
            [b.from]: [...(a[b.from] || []), b],
          }
        }, {})

        let dispatchMessages = fromId => {
          if (chatMessages[fromId]) {
            const payload = [...chatMessages[fromId]]
            dispatch({ type: GET_CHAT_MESSAGES_SUCCESS, chatId: fromId, payload })
            // delete chatMessages[fromId];
          }
        }

        // console.log('listenMyChatMessages > chatMessages',{chatMessages});
        try {
          const chatDoc = await chatRef.get()
          if (chatDoc.exists) {
            const chatData = chatDoc.data()
            // console.log('listenMyChatMessages > chatData',{chatData});
            ;(chatData.users || []).forEach(fromId => {
              if (listeners[fromId]) {
                dispatchMessages(fromId)
              } else {
                let list = FirestoreRef.collection(collectionName)
                  .doc(fromId)
                  .onSnapshot(
                    doc => {
                      if (!doc.exists) {
                        dispatchMessages(fromId)
                        return
                      }
                      const chats = [doc.data()]
                      // console.log('listenMyChatMessages > chats',chats);
                      dispatch({
                        type: GET_CHAT_USERS_SUCCESS,
                        payload: chats,
                      })
                      dispatchMessages(fromId)
                    },
                    err => {
                      console.log('listenMyChatMessages > err', err)
                      dispatch({
                        type: GET_CHAT_USERS_ERROR,
                      })
                    }
                  )
                listeners[fromId] = [list]
              }
            })
          }
        } catch (error) {
          console.error(error)
        }
        // add listener to list
        listeners[qiuserId].push(listener)
      },
      err => {
        console.log('listenMyChatMessages > err', err)
        dispatch({
          type: GET_CHAT_MESSAGES_ERROR,
        })
      }
    )
}

export const getMoreChatMessages = (chatId, isGroup, qiuserId) => (dispatch, getState) => {
  let refs = {
    to: FirestoreRef.collection(collectionName).doc(chatId).collection(MESSAGES_SUBCOLLECTION_NAME).where('to', '==', qiuserId),
    from: FirestoreRef.collection(collectionName).doc(chatId).collection(MESSAGES_SUBCOLLECTION_NAME).where('from', '==', qiuserId),
  }

  Object.values(refs).forEach(ref => {
    // single query to get new startAt snapshot
    ref
      .orderBy(CREATED_FIELD, 'desc')
      .startAt(start[chatId])
      .limit(50)
      .get()
      .then(snapshot => {
        // previous starting boundary becomes new ending boundary
        end[chatId] = start[chatId]
        start[chatId] = snapshot.docs[snapshot.docs.length - 1]

        // create another listener using new boundaries
        let listener = ref
          .orderBy(CREATED_FIELD)
          .startAt(start[chatId])
          .endBefore(end[chatId])
          .onSnapshot(({ docs }) => {
            const messages = docs.map(doc => doc.data())
            dispatch({
              type: GET_CHAT_MESSAGES_SUCCESS,
              chatId,
              payload: messages,
            })
          })

        listeners[chatId].push(listener)
      })
  })
}

// call to detach all listeners
export const detachChatMessagesListeners = chatId => {
  listeners[chatId].forEach(listener => listener())
}

export const updateMyChatMessages = (chatId, updates, queries) => (dispatch, getState) => {
  const {
    authReducer: { user },
  } = getState()
  const qiuserId = user.ID
  return new Promise(res => {
    let fromRef = FirestoreRef.collection(collectionName).doc(qiuserId).collection(MESSAGES_SUBCOLLECTION_NAME).where('from', '==', chatId)
    Array.isArray(queries) && queries.forEach(queryArgs => (fromRef = fromRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))
    fromRef.get().then(snapshot => {
      return Promise.all(snapshot.docs.map(doc => doc.ref.update(updates))).then(() => {
        res(snapshot.docs.map(d => ({ ...d.data(), ...updates })))
      })
    })
  })
}

export const addChatMessage = (chatId, message, qiuserId) => (dispatch, getState) => {
  const {
    authReducer: { user },
  } = getState()
  qiuserId = qiuserId || user.ID

  let timestamp = new Date().getTime()

  dispatch({
    type: ADD_CHAT_MESSAGE,
    timestamp,
    chatId,
    payload: message,
  })

  const toRef = FirestoreRef.collection(collectionName).doc(chatId)

  toRef
    .collection(MESSAGES_SUBCOLLECTION_NAME)
    .add({
      ...message,
      createdAt: timestamp,
      timestamp,
      to: chatId,
      from: qiuserId,
    })
    .then(result => {
      // console.log({result});
      dispatch({
        type: ADD_CHAT_MESSAGE_SUCCESS,
        timestamp,
        chatId,
        payload: message,
      })
    })
    .catch(err => {
      console.error(err)
      dispatch({
        type: ADD_CHAT_MESSAGE_ERROR,
        timestamp,
        chatId,
        error: err,
      })
    })
}
