import { CONTACTS_SUBCOLLECTION_NAME, COUNTERS_COLLECTION_NAME, INSTANCES_COLLECTION_NAME, LEADS_COLLECTION_NAME, MESSAGES_SUBCOLLECTION_NAME, SHOTX_COLLECTION_NAME } from "Constants/AppCollections"
import { getCurrentUserToken } from 'FirebaseRef'
import { FirebaseRepository } from "FirebaseRef/repository"
import { axiosChatService } from "Routes/chat/API/axiosChatService"

axiosChatService.interceptors.request.use(async config => {
  const token = await getCurrentUserToken()
  config.headers = {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  }
  return config
})

export class ShotXActions {

  /**
   * Function listen User Messages
   *
   * @param {string} accountId
   * @param {string} contactId
   * @param {function} callback
   */

  listenUserMessages = (accountId, instanceID, contactId, callback, where) => {
    const path = `${SHOTX_COLLECTION_NAME}/${accountId}/${INSTANCES_COLLECTION_NAME}/${instanceID}/${CONTACTS_SUBCOLLECTION_NAME}/${contactId}/${MESSAGES_SUBCOLLECTION_NAME}`

    if (path.includes('undefined')) {
      console.error('path is invalid')
      console.error(path)
      return
    }

    const repository = new FirebaseRepository()

    const onSuccess = chats => {
      callback && callback(chats)
    }

    const onError = err => {
      console.error(err)
      callback && callback([])
    }

    return repository.listenCollection(path, onSuccess, onError, where)
  }

  fetchContact = (leadId, callback) => {
    if (!leadId) Error('leadId is required')

    const repository = new FirebaseRepository()

    return new Promise(res => {
      const path = `${LEADS_COLLECTION_NAME}/${leadId}`
      repository.getDoc(
        path,
        leads => {
          callback && callback(leads)
          res(leads)
        },
        err => {
          console.error(err)
        }
      )
    })
  }

  fetchInstanceContact = (
    accountId,
    instanceID,
    contactID,
    callback
  ) => {
    if (!accountId || !instanceID || !contactID) Error('accountId, instanceID and contactID are required')

    const repository = new FirebaseRepository()

    return new Promise(res => {
      const path = `${SHOTX_COLLECTION_NAME}/${accountId}/${INSTANCES_COLLECTION_NAME}/${instanceID}/${CONTACTS_SUBCOLLECTION_NAME}/${contactID}`
      repository.getDoc(
        path,
        contact => {
          callback && callback(contact)
          res(contact)
        },
        err => {
          console.error(err)
        }
      )
    })
  }

  contactExists = (accountId, instanceID, contactID) => {
    const repository = new FirebaseRepository()

    return new Promise(res => {
      const path = `${SHOTX_COLLECTION_NAME}/${accountId}/${INSTANCES_COLLECTION_NAME}/${instanceID}/${CONTACTS_SUBCOLLECTION_NAME}/${contactID}/${MESSAGES_SUBCOLLECTION_NAME}`

      res(repository.collectionExists(path))
    })
  }

  resetMessagesCount = (accountId, instanceID, where = []) => {
    if (!accountId || !instanceID) {
      console.error('accountId and instanceID are required')
      return
    }
    const repository = new FirebaseRepository()
    const wheres = [
      ['accountId', '==', accountId],
      ['instanceId', '==', instanceID],
      ...where,
    ]

    repository.deleteDocWhere(COUNTERS_COLLECTION_NAME, wheres)
  }

  updateSniperSession = async ({
    action,
    accountId,
    contactId,
    instanceId,
    platform
  }) => {

    const body = { accountId, instanceId, action, contactId, platform }

    return await axiosChatService.put(`/sniper/change_session/${instanceId}`, body)
  }

  getCurrentSession = async ({ contactId, body }) => {
    return await axiosChatService.post(`/sniper/getCurrentSession/${contactId}`, body)
  }

  sendToSniper = async ({ contactId, body }) => {
    return await axiosChatService.post(`/sniper/sendToSniper/${contactId}`, body)
  }
}
