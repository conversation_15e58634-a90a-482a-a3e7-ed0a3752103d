/**
 * Email App Actions
 */

import { FirestoreRef, sendSMTPMail, saveIMAPMail, moveIMAPMail, getIMAPMail, getIMAPFolders } from '../firebase'

import moment from 'moment'

import { langMessages } from '../lang'

import {
  ON_IMAP_AUTH_SUCCESS,
  ON_IMAP_AUTH_ERROR,
  ON_SMTP_TEST_SUCCESS,
  ON_SMTP_TEST_ERROR,
  SAVE_IMAP_DRAFT,
  SAVE_IMAP_DRAFT_SUCCESS,
  SAVE_IMAP_DRAFT_FAILURE,
  ON_EMAIL_MOVE_TO_FOLDER,
  ON_EMAIL_MOVE_TO_FOLDER_SUCCESS,
  ON_EMAIL_MOVE_TO_FOLDER_FAILURE,
  GET_MAILBOX_LABELS,
  GET_MAILBOX_LABELS_SUCCESS,
  GET_MAILBOX_LABELS_FAILURE,
  GET_EMAIL,
  GET_EMAIL_SUCCESS,
  GET_EMAIL_FAILURE,
  SET_EMAIL_AS_STAR,
  SEARCH_EMAIL,
  ON_SEND_EMAIL,
  ON_SEND_EMAIL_SUCCESS,
  EMAIL_SENT_SUCCESSFULLY,
  ON_SEND_EMAIL_FAILURE,
  GET_FOLDER_EMAILS,
  GET_FOLDER_EMAILS_SUCCESS,
  GET_FOLDER_EMAILS_FAILURE,
  GET_MAILBOX_FOLDERS,
  GET_MAILBOX_FOLDERS_SUCCESS,
  GET_MAILBOX_FOLDERS_FAILURE,
  ADD_LABELS_INTO_EMAILS,
} from 'Actions/types'

import { validateEmail, extractEmailAddress } from 'Helpers/formHelpers'

import { MOMENT_ISO } from 'Constants'

// folders
import folders from 'Routes/mail/model/folders'
import folderModel from 'Routes/mail/model/folder'
import emailModel from 'Routes/mail/model/email'

import COLLECTIONS from '../constants/AppCollections'
import { localJSON } from '../helpers/helpers'
import { initGapiScripts } from './GmailAppActions'

const utf8 = require('utf8')
const quotedPrintable = require('quoted-printable')

const parseImapMessage = message => {
  const {
    seqno,
    header,
    header: { from, date },
    body,
    html,
    attributes,
    attributes: { flags, uid },
    attachments,
  } = message
  const extractFromHeader = f => (((header[f] || [])[0] || '').split('<')[1] || (header[f] || [])[0] || '').split('>')[0]

  let htmlContent = html || body

  try {
    htmlContent = utf8.decode(quotedPrintable.decode(htmlContent))
  } catch (error) {
    htmlContent = quotedPrintable.decode(htmlContent)
  }

  ;[/--[^\n\r]*\r?\nContent-Type: text\/html[\s\S]*?\r?\n\r?\n/g, /--[^\n\r]*\r?\nContent-Type: text\/plain[\s\S]*?\r?\n\r?\n/g].forEach(regEx => {
    let info = regEx.exec(htmlContent)
    if (info) {
      let part = (info[0].match(/--[^\n\r]*\r?/g) || [])[0]
      let content = htmlContent.split(info)[1]
      if (part && (content.match(part) || [])[0]) {
        htmlContent = content.split(part)[0]
      } else {
        let nextInfo = /\n--[^\n\r]*\r?\nContent-Type[\s\S]*?\r?\n\r?\n/g.exec(content)
        htmlContent = content.split(nextInfo[0])[0]
      }
    }
  })

  if (htmlContent && htmlContent.match(/<body(.*)>/)) {
    let match = (htmlContent.match(/<body(.*)>/) || [])[0]
    let content = htmlContent.split(match)[1].split('</body>')[0]
    htmlContent = `${match.replace('body', 'div')}${content}${'</div>'}`
  }

  let eDate = moment((date && date[0]) || attributes.date).isValid()
    ? moment((date && date[0]) || attributes.date).format(MOMENT_ISO)
    : moment().format(MOMENT_ISO)

  let email = {
    ...emailModel,
    attachments,
    html: String(htmlContent),
    id: `${seqno}`,
    ID: `${seqno}`,
    mailId: `${seqno}`,
    resourceId: uid,
    date: moment(eDate).format(MOMENT_ISO),
    modified: moment(eDate).format(MOMENT_ISO),
    createdAt: new Date(eDate).getTime(),
    updatedAt: new Date(eDate).getTime(),
    labels: Array.isArray(flags) && flags.map(f => String(f).replace(/\\/g, '')),
    subject: extractFromHeader('subject'),
    to: extractFromHeader('to'),
    fromName: ((from || [])[0] || '').split('<')[0].replace(/"/g, ''),
    from: extractFromHeader('from'),
    cc: extractFromHeader('cc'),
    bcc: extractFromHeader('bcc'),
  }

  // console.groupCollapsed('parseImapMessage > message');
  // console.log('message',message);
  // console.log('email',email);
  // console.log('attachments',attachments);
  // console.groupEnd('parseImapMessage > message');

  return email
}

/**
 * Redux Action To Mark As Star Email
 */
export const imapMarkAsStarEmail = email => ({
  type: SET_EMAIL_AS_STAR,
  payload: email,
})

/**
 * Redux Action To Search Email
 */
export const imapSearchEmail = searchText => ({
  type: SEARCH_EMAIL,
  payload: searchText,
})

/**
 * Redux Action To Add Labels Into Email
 */
export const imapAddLabelsIntoEmail = label => ({
  type: ADD_LABELS_INTO_EMAILS,
  payload: label,
})

export const testImapUser = config => (dispatch, getState) => {
  return new Promise((res, rej) => {
    getIMAPFolders({ config })
      .then(response => {
        console.log('testImapUser > response', response)
        const { data, error } = response
        if (data.results && data.results.folders) {
          dispatch({ type: ON_IMAP_AUTH_SUCCESS })
          return res(true)
        } else {
          dispatch({ type: ON_IMAP_AUTH_ERROR, error })
          return res(false)
        }
      })
      .catch(err => {
        dispatch({ type: ON_IMAP_AUTH_ERROR, error: err })
        return res(false)
      })
  })
}

export const testSMTPUser = config => (dispatch, getState) => {
  return new Promise((res, rej) => {
    const currentUser = localJSON.get('user')
    const { email, displayName } = currentUser

    const { user, name } = config
    const formattedFrom = name ? `${name} <${user}>` : user

    const message = {
      subject: `QIPlus || ${langMessages['alerts.emailSMTPSuccess']}`,
      from: formattedFrom,
      html: langMessages['placeholders.SMTP_SETTINGS_CONFIRM_EMAAIL'].replace('[%s]', displayName || name),
      to: email || user,
    }

    let onError = err => {
      dispatch({ type: ON_SMTP_TEST_ERROR, error: err })
      res(false)
    }

    try {
      sendSMTPMail({ config, message })
        .then(response => {
          console.log('testSMTPUser > response', response)
          const { data, error } = response
          if (data.status === true) {
            dispatch({ type: ON_SMTP_TEST_SUCCESS })
            return res(true)
          } else {
            return onError(error)
          }
        })
        .catch(onError)
    } catch (error) {
      onError(error)
    }
  })
}

export const getImapMessage = (mailboxId, folder, config, messageId, useStore) => (dispatch, getState) => {
  useStore = useStore !== false

  useStore && dispatch({ type: GET_EMAIL, folder })

  return new Promise((res, rej) => {
    let request = { ...config, action: 'read', range: `${messageId}:${messageId}` }

    getIMAPMail(request)
      .then(response => {
        console.log('getImapMessage > request', request)
        console.log('getImapMessage > response', response)
        const { data, error } = response
        if (data.results.messages && data.results.messages[`${messageId}`]) {
          let message = data.results.messages[`${messageId}`]
          try {
            let email = parseImapMessage(message)
            dispatch({ type: GET_EMAIL_SUCCESS, payload: email, folder, mailboxId })
            return res({ email, response })
          } catch (error) {
            return rej({ error, message: langMessages['errors.fetchErrorMsg'] })
          }
        }
        return rej(error || { message: langMessages['errors.fetchErrorMsg'] })
      })
      .catch(rej)
  }).catch(err => {
    console.error({ err })
    if (err && err.message === 'internal') {
      err.message = langMessages['errors.fetchErrorMsg']
    }
    useStore && dispatch({ type: GET_EMAIL_FAILURE, ID: messageId, folder, error: err.message || err.error || langMessages['errors.fetchErrorMsg'] })
    return 'Erro na integração imap'
  })
}

export const fetchImapMessages = (mailboxId, folder, config, query) => (dispatch, getState) => {
  dispatch({ type: GET_FOLDER_EMAILS, folder, query })

  return new Promise((res, rej) => {
    let request = { ...config, action: 'list', range: 'last' }

    getIMAPMail(request)
      .then(response => {
        console.log('fetchImapMessages > response', response)
        const { data, error } = response
        if (data.results.messages) {
          const { total, unseen } = data.results

          let messages = []

          if (Object.keys(data.results.messages).length) {
            messages = Object.keys(data.results.messages).map((n, i) => {
              const message = parseImapMessage(data.results.messages[n])
              return message
            })
          }

          dispatch({ type: GET_FOLDER_EMAILS_SUCCESS, payload: messages, folder, mailboxId, total, unseen, new: data.results.new })
          return res(messages)
        } else if (data.results.end) {
          dispatch({ type: GET_FOLDER_EMAILS_SUCCESS, payload: [], folder, mailboxId, total: 0, unseen: 0, new: 0 })
          return res([])
        }
        return rej(error || { message: 'Erro na integração imap' })
      })
      .catch(rej)
  }).catch(err => {
    console.error({ err })
    if (err && err.message === 'internal') {
      err.message = langMessages['errors.fetchErrorMsg']
    }
    dispatch({ type: GET_FOLDER_EMAILS_FAILURE, folder, error: err.message || err.error || langMessages['errors.fetchErrorMsg'] })
    return 'Erro na integração imap'
  })
}

export const moveImapMessage = (mailboxId, fromFolder, toFolder, config, email) => (dispatch, getState) => {
  console.log('moveImapMessage > email', { mailboxId, fromFolder, toFolder, config, email })

  dispatch({ type: ON_EMAIL_MOVE_TO_FOLDER, fromFolder, toFolder, mailboxId })

  const { resourceId, mailId } = email

  return new Promise((res, rej) => {
    let request = { ...config, uid: `${resourceId}` }
    console.log('moveImapMessage > request', { request })

    try {
      moveIMAPMail(request).then(response => {
        console.log('moveImapMessage > response', response)
        const { data, error } = response

        if (data.results.status) {
          let fetch = { ...config, range: 'last', action: 'read', page: 1, perpage: 0 }
          getIMAPMail(fetch)
            .then(resp => {
              console.log('moveImapMessage > fetch', fetch)
              console.log('moveImapMessage > get', resp)
              if (resp.data.results.messages && Object.keys(resp.data.results.messages).length) {
                let m = Object.keys(resp.data.results.messages).map((n, i) => {
                  return parseImapMessage(resp.data.results.messages[n])
                })
                let e = m[0]
                dispatch({ type: ON_EMAIL_MOVE_TO_FOLDER_SUCCESS, fromFolder, toFolder, mailboxId, mailId })
                return res(e)
              }
              return rej(resp.error || { message: langMessages['errors.fetchErrorMsg'] })
            })
            .catch(rej)
        } else rej(error || { message: langMessages['errors.fetchErrorMsg'] })
      })
    } catch (error) {
      rej(error || { message: langMessages['errors.fetchErrorMsg'] })
    }
  }).catch(err => {
    console.log('moveImapMessage > err', err)
    if (err && err.message === 'internal') {
      err.message = langMessages['errors.fetchErrorMsg']
    }
    const message = err.message || err.error || langMessages['errors.fetchErrorMsg']
    dispatch({ type: ON_EMAIL_MOVE_TO_FOLDER_FAILURE, fromFolder, toFolder, mailboxId, mailId, error: message })
  })
}

export const sendSMTPMessage = (mailboxId, data, config) => (dispatch, getState) => {
  return new Promise((resolve, reject) => {
    const { fromName, subject, html, to, cc, bcc } = data
    const { user, name } = config

    const formattedFrom = fromName || name ? `${fromName || name} <${user}>` : user
    const message = { to, cc, bcc, from: formattedFrom, subject, html }

    if (!cc) delete message.cc
    if (!bcc) delete message.bcc

    let rejected = false
    let requiredFields = ['from', 'subject', 'html', 'to']

    requiredFields.forEach(field => {
      if (!rejected && !message[field]) {
        rejected = true
        reject({ error: langMessages['placeholders.requiredField'].replace('[%s]', langMessages[`email.fields.${field}`]) })
      }
    })
    ;[{ to }, { cc: cc || '' }, { bcc: bcc || '' }].forEach(o => {
      Object.keys(o).forEach(f => {
        o[f] &&
          o[f].split(',').forEach(e => {
            if (!rejected && !validateEmail(extractEmailAddress(e))) {
              rejected = true
              return reject({ error: langMessages['placeholders.invalidField'].replace('[%s]', langMessages[`email.fields.${f}`]) })
            }
          })
      })
    })

    if (rejected) return

    console.log('sendSMTPMessage > data', data)

    dispatch({ type: ON_SEND_EMAIL })

    let onError = err => {
      console.log('sendSMTPMessage > err', err)
      if (err && err.message === 'internal') {
        err.message = langMessages['errors.fetchErrorMsg']
      }
      const message = err.message || err.error || err || 'mail.errors.errorSendingEmail'
      dispatch({ type: ON_SEND_EMAIL_FAILURE, error: langMessages[message] || message, mailboxId })
      reject({ error: langMessages[message] || message })
    }

    try {
      sendSMTPMail({ config, message })
        .then(response => {
          console.log('testImapUser > response', response)
          const { data, error } = response
          if (data.status === true) {
            dispatch({ type: ON_SEND_EMAIL_SUCCESS, mailboxId })
            dispatch({ type: EMAIL_SENT_SUCCESSFULLY, mailboxId })

            return resolve(message)
          } else {
            onError(error || { message: langMessages['mail.errors.errorSendingEmail'] })
          }
        })
        .catch(onError)
    } catch (error) {
      onError(error)
    }
  })
}

export const sendImapDraft = (mailboxId, email, srcMailbox) => (dispatch, getState) => {
  console.log('srcMailbox', srcMailbox)

  const { folders, auth, smtp } = srcMailbox

  return sendSMTPMessage(
    mailboxId,
    email,
    smtp
  )(dispatch).then(e => {
    let fromFolder = 'draft',
      folder = fromFolder
    try {
      let f = folders.find(f => f.id === folder)
      let delimiter = (f.source.data || {}).delimiter || f.source.delimiter || '/'
      folder = (f.source.parent ? `${f.source.parent}${delimiter}` : '') + `${f.source.key}`
    } catch (error) {
      folder = fromFolder
    }

    let toFolder = 'sent',
      destination = toFolder
    try {
      let f = folders.find(f => f.id === destination)
      let delimiter = (f.source.data || {}).delimiter || f.source.delimiter || '/'
      destination = (f.source.parent ? `${f.source.parent}${delimiter}` : '') + `${f.source.key}`
    } catch (error) {
      destination = toFolder
    }

    let request = { folder, destination, config: auth }
    moveImapMessage(mailboxId, fromFolder, toFolder, request, email)(dispatch)
  })
}

export const saveImapDraft = (mailboxId, folder, config, email) => (dispatch, getState) => {
  console.log('saveImapDraft > email', email)

  dispatch({ type: SAVE_IMAP_DRAFT, mailboxId, folder })

  return new Promise((res, rej) => {
    const { id, resourceId, from, fromName, subject, html, to, cc, bcc } = email
    // const isNewDraft = !resourceId;

    let fromMkp = `${fromName} <${extractEmailAddress(from)}>`
    let mimeDate = moment().format('ddd, DD MMM YYYY HH:mm:s -0300')

    let message =
      'Content-Type: text/html; charset="UTF-8"\r\n' +
      'MIME-Version: 1.0\r\n' +
      'Content-Transfer-Encoding: 7bit\r\n' +
      'Date: ' +
      mimeDate +
      '\r\n' +
      'Subject: ' +
      subject +
      '\r\n' +
      'From: ' +
      fromMkp +
      '\r\n'

    if (bcc) message += 'Bcc: ' + bcc + '\r\n'
    if (cc) message += 'Cc: ' + cc + '\r\n'

    message += 'To: ' + to + '\r\n\r\n'

    let fullMessage = message + html

    let options = { ...(config.options || {}), date: (config.options || {}).date || moment().format() }
    let request = { ...config, uid: resourceId, options, message: fullMessage }

    try {
      saveIMAPMail(request).then(response => {
        console.log('saveImapDraft > response', response)
        const { data, error } = response

        if (data.results.status) {
          let fetch = { ...config, range: 'last', action: 'read', page: 1, perpage: 0 }
          getIMAPMail(fetch)
            .then(resp => {
              console.log('saveImapDraft > fetch', fetch)
              console.log('saveImapDraft > get', resp)
              if (resp.data.results.messages && Object.keys(resp.data.results.messages).length) {
                let m = Object.keys(resp.data.results.messages).map((n, i) => {
                  return parseImapMessage(resp.data.results.messages[n])
                })
                let e = m[0]
                dispatch({ type: SAVE_IMAP_DRAFT_SUCCESS, mailboxId, payload: e, folder, prevId: id })
                return res(e)
              }
              return rej(resp.error || { message: langMessages['errors.fetchErrorMsg'] })
            })
            .catch(rej)
        } else rej(error || { message: langMessages['errors.fetchErrorMsg'] })
      })
    } catch (error) {
      rej(error || { message: langMessages['errors.fetchErrorMsg'] })
    }
  }).catch(err => {
    console.log('err', err)
    if (err && err.message === 'internal') {
      err.message = langMessages['errors.fetchErrorMsg']
    }
    const message = err.message || err.error || langMessages['errors.fetchErrorMsg']
    dispatch({ type: SAVE_IMAP_DRAFT_FAILURE, mailboxId, folder, error: message })
  })
}

export const onTrashImapMessage = (mailboxId, folder, config, email) => (dispatch, getState) => {
  return moveImapMessage(mailboxId, folder, 'trash', config, email)(dispatch)
}

export const fetchIMAPFolders = (mailboxId, auth) => (dispatch, getState) => {
  dispatch({ type: GET_MAILBOX_FOLDERS, mailboxId })

  return new Promise((res, rej) => {
    getIMAPFolders({ config: auth })
      .then(response => {
        const { data, error } = response
        if (data.results.folders) {
          let mailboxFolders = data.results.folders
            .reduce((a, b) => {
              let f = b,
                { children, name, data } = f
              let { delimiter } = data || {}
              return [...a, { ...f, children: null }, ...(children || []).map(c => ({ ...c, parent: name, delimiter: delimiter || '/' }))]
            }, [])
            // .filter(filterFolders)
            .map((f, k) => {
              // let handlers = ['inbox','sent','draft','spam','trash']
              // let replacements = ['inbox','sent','drafts','junk','trash']
              const { name, data } = f
              const { special_use_attrib } = data || {}
              let d,
                t,
                n = (name.replace(/ /g, '') || '').toLowerCase().replace('junk', 'spam').replace('drafts', 'draft')
              if (special_use_attrib) {
                t = (special_use_attrib.replace(/\\| /g, '') || '').toLowerCase().replace('junk', 'spam').replace('drafts', 'draft')
                d = folders.find(f => f.type === t || f.id === t)
                // console.log({ d, t });
              }
              d = d || folders.find(f => f.id === n || f.handle === n)
              return {
                ...folderModel,
                source: f,
                title: d ? d.title : name,
                id: d ? d.id : name,
                handle: d ? d.handle : name,
                pos: d ? d.pos : folders.length + k + 1,
                type: d ? d.type : 'inbox',
                icon: d ? d.icon : 'zmdi zmdi-folder',
              }
            })

          console.log('mailboxFolders', mailboxFolders)

          dispatch({ type: GET_MAILBOX_FOLDERS_SUCCESS, payload: mailboxFolders, mailboxId })

          FirestoreRef.collection(COLLECTIONS.MAILBOXES_COLLECTION_NAME).doc(mailboxId).update({ folders: mailboxFolders })

          return res(mailboxFolders)
        }

        console.error('error', error)
        dispatch({ type: GET_MAILBOX_FOLDERS_FAILURE, mailboxId })
        return rej(error)
      })
      .catch(error => {
        console.error('error', error)
        dispatch({ type: GET_MAILBOX_FOLDERS_FAILURE, mailboxId })
        return rej(error)
      })
  })
}

export const fetchImapLabels = (mailboxId, userId) => (dispatch, getState) => {
  dispatch({ type: GET_MAILBOX_LABELS, mailboxId })

  return new Promise((res, rej) => {
    initGapiScripts()(dispatch).then(gapi => {
      let signinStatus = gapi.auth2.getAuthInstance().isSignedIn.get()

      if (!signinStatus) {
        dispatch({ type: GET_MAILBOX_LABELS_FAILURE, mailboxId })
        return rej('Usuaário não autenticado no gmail')
      }

      let request = gapi.client.gmail.users.labels.list({
        userId,
      })

      request.execute(
        resp => {
          // console.log('resp',resp);
          let labels = (resp.labels || [])
            .filter(label => label.id && label.id.indexOf('CATEGORY_') !== 0 && !folders.find(f => f.id.toLowerCase() === label.id.toLowerCase()))
            .map(l => ({
              ...l,
              title: `mailboxes.labels.${l.name}` in langMessages ? langMessages[`mailboxes.labels.${l.name}`] : l.name,
            }))

          dispatch({ type: GET_MAILBOX_LABELS_SUCCESS, payload: labels, mailboxId })

          FirestoreRef.collection(COLLECTIONS.MAILBOXES_COLLECTION_NAME).doc(mailboxId).update({ labels })

          return res(labels)
        },
        err => {
          console.error('error', err)
          dispatch({ type: GET_MAILBOX_LABELS_FAILURE, mailboxId })
          return rej(err.error)
        }
      )
    })
  })
}
