/**
 * Deals Actions
 */

import { OPEN_DIALOG, CLOSE_DIALOG, DIALOG_RESULTS } from 'Actions/types'

import DialogModel from 'Models/childnodes/dialog'

export const openDialog = data => ({
  type: OPEN_DIALOG,
  ...window.jsonClone(DialogModel),
  ...data,
})

export const closeDialog = data => ({
  type: CLOSE_DIALOG,
})

export const dialogResults = data => ({
  type: DIALOG_RESULTS,
  payload: data,
})
