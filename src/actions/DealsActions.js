/**
 * Deals Actions
 */
import { FirestoreRef } from 'FirebaseRef'

import {
  SAVE_NEW_DEAL,
  SAVE_NEW_DEAL_FAILURE,
  SAVE_NEW_DEAL_SUCCESS,
  SAVE_NEW_POST_SUCCESS,
  SAVE_POST_SUCCESS,
  SAVE_POST_FIELDS_SUCCESS,
  SAVE_DEAL,
  SAVE_DEAL_SUCCESS,
  SAVE_DEAL_FAILURE,
  SAVE_DEAL_FIELDS,
  SAVE_DEAL_FIELDS_SUCCESS,
  SAVE_DEAL_FIELDS_FAILURE,
} from './types'

import moment from 'moment'
import { DEFAULT_LOCALE, DEALS_COLLECTION_NAME, MOMENT_ISO } from 'Constants'
import { sessionJSON, localJSON } from 'Helpers/helpers'
import { OWNER_LEVEL, MANAGER_LEVEL, SELLER_LEVEL, OWNER_ROLE, MANAGER_ROLE, SELLER_ROLE } from '../constants/UsersRoles'

import DealModel from 'Routes/deals/model'
import { replaceUndefined } from '../helpers/helpers'

const collectionName = DEALS_COLLECTION_NAME

/**
 * Redux Action Save New Deal
 */
export const saveNewDeal = data => (dispatch, getState) => {
  dispatch({ type: SAVE_NEW_DEAL })
  return new Promise((resolve, reject) => {
    const newDeal = sanitizeDeal(data, 'add')

    if (!newDeal.owner) {
      dispatch({ type: SAVE_NEW_DEAL_FAILURE })
      return reject()
    }

    FirestoreRef.collection(collectionName)
      .add(newDeal)
      .then(doc => {
        const payload = {
          ...newDeal,
          id: doc.id,
          ID: doc.id,
        }
        console.log('saveNewDeal > payload', payload)
        dispatch({ type: SAVE_NEW_DEAL_SUCCESS, payload })
        dispatch({ type: SAVE_NEW_POST_SUCCESS, collection: collectionName, payload })
        return resolve(payload)
      })
      .catch(function (error) {
        dispatch({ type: SAVE_NEW_DEAL_FAILURE })
        return reject(error)
      })
  })
}

export const saveDealFields = (dealId, data) => (dispatch, getState) => {
  dealId = dealId || data.ID
  data = replaceUndefined(data)

  dispatch({ type: SAVE_DEAL_FIELDS })
  return new Promise((resolve, reject) => {
    if (!dealId) {
      dispatch({ type: SAVE_DEAL_FIELDS_FAILURE })
      return reject('SAVE_DEAL_FIELDS_FAILURE: No ID provided')
    }

    const user = localJSON.get('user', false)

    const updatedDealData = {
      ...data,
      modified: moment().format(MOMENT_ISO),
      updatedBy: user.ID,
    }

    // console.log('saveDealFields > deal',updatedDealData);
    FirestoreRef.collection(collectionName)
      .doc(dealId)
      .update(updatedDealData)
      .then(() => {
        const payload = updatedDealData
        dispatch({ type: SAVE_DEAL_FIELDS_SUCCESS, payload })
        dispatch({ type: SAVE_POST_FIELDS_SUCCESS, collection: collectionName, payload, postId: dealId })
        return resolve(payload)
      })
      .catch(function (error) {
        dispatch({ type: SAVE_DEAL_FIELDS_FAILURE })
        return reject(error)
      })
  })
}

export const saveDeal = data => (dispatch, getState) => {
  const { ID } = data
  dispatch({ type: SAVE_DEAL })
  return new Promise((resolve, reject) => {
    if (!ID) {
      dispatch({ type: SAVE_DEAL_FAILURE })
      return reject('SAVE_DEAL_FAILURE: No ID provided')
    }

    const deal = sanitizeDeal(data, 'update')
    // console.log('saveDeal > deal',deal);

    FirestoreRef.collection(collectionName)
      .doc(ID)
      .update(deal)
      .then(() => {
        const payload = deal
        dispatch({ type: SAVE_DEAL_SUCCESS, payload })
        dispatch({ type: SAVE_POST_SUCCESS, collection: collectionName, payload })
        return resolve(payload)
      })
      .catch(function (error) {
        dispatch({ type: SAVE_DEAL_FAILURE })
        return reject(error)
      })
  })
}

export const sanitizeDeal = (data, prepare) => {
  data = replaceUndefined(data)

  const user = localJSON.get('user', false)

  // contact
  const dealContact = window.jsonClone(DealModel.contact)
  const contact = data.contact || dealContact
  Object.keys(dealContact).forEach((k, i) => {
    if (k in contact) dealContact[k] = contact[k]
  })
  data.contact = dealContact
  // end contact

  // owner
  const currentOwnerID = sessionJSON.get('currentOwnerID', false)
  const ownerId = data.owner || currentOwnerID || user.owner || (user.level <= OWNER_LEVEL && user.ID) || ''
  const currentAccountID = sessionJSON.get('account', {}).ID || (user || {}).accountId
  const accountId = data.accountId || currentAccountID || user.accountId
  // end owner

  const seller = data.seller || ((user.roles || []).includes(SELLER_ROLE) && user.ID) || ''
  const manager = data.manager || user.manager || ((user.roles || []).includes(MANAGER_ROLE) && user.ID) || ''

  let dbData = {
    ...window.jsonClone(data),
    collection: collectionName,
    locale: data.locale || DEFAULT_LOCALE,
    seller,
    manager,
    owner: ownerId,
    accountId,
  }

  switch (prepare) {
    case 'add':
      dbData = {
        ...dbData,
        date: moment().format(MOMENT_ISO),
        modified: moment().format(MOMENT_ISO),
        author: user.ID,
        logs: {
          ...(data.logs || {}),
          added: {
            user: user.ID,
            operator_id: user.ID,
            date: moment().format(MOMENT_ISO),
          },
        },
      }

      break

    case 'update':
    default:
      dbData = {
        ...dbData,
        modified: moment().format(MOMENT_ISO),
        updatedBy: user.ID,
        logs: {
          ...(data.logs || {}),
          updated: {
            user: user.ID,
            operator_id: user.ID,
            date: moment().format(MOMENT_ISO),
          },
        },
      }

      break
  }

  return dbData
}
