/**
 * Campaigns Actions
 */
import { FirestoreRef } from 'FirebaseRef'

// lang strings
import { langMessages } from '../lang'

import {
  GET_CAMPAIGNS_PARTICIPANTS,
  GET_CAMPAIGNS_PARTICIPANTS_SUCCESS,
  GET_CAMPAIGNS_PARTICIPANTS_FAILURE,
  GET_CAMPAIGN_PARTICIPANTS,
  GET_CAMPAIGN_PARTICIPANTS_SUCCESS,
  GET_CAMPAIGN_PARTICIPANTS_FAILURE,
} from 'Actions/types'

import { DOC_DOESNT_EXIST } from '../constants/AppConstants'

import { CAMPAIGNS_COLLECTION_NAME, PARTICIPANTS_COLLECTION_NAME } from '../constants/AppCollections'

const collectionName = CAMPAIGNS_COLLECTION_NAME
const participantsCollection = PARTICIPANTS_COLLECTION_NAME

/**
 * Redux Action Get Participants
 */

export const getCampaignsParticipants = eventIds => (dispatch, getState) => {
  if (!Array.isArray(eventIds)) {
    dispatch({ type: GET_CAMPAIGNS_PARTICIPANTS_FAILURE })
    return
  }

  dispatch({ type: GET_CAMPAIGNS_PARTICIPANTS })

  return new Promise((resolve, reject) => {
    const posts = {}
    let counter = eventIds.length
    let errCounter = 0

    eventIds.forEach(eventId => {
      posts[eventId] = {}
      getCampaignParticipants(eventId)(action => {
        switch (action.type) {
          case GET_CAMPAIGN_PARTICIPANTS_SUCCESS:
            posts[action.ID] = action.payload
            counter--
            break
          case GET_CAMPAIGN_PARTICIPANTS_FAILURE:
            counter--
            errCounter++
            break
          default:
            break
        }
        if (counter === 0) {
          if (errCounter > 0) {
            dispatch({ type: GET_CAMPAIGNS_PARTICIPANTS_FAILURE })
            // return reject();
          }
          dispatch({ type: GET_CAMPAIGNS_PARTICIPANTS_SUCCESS, payload: posts })
          return resolve(posts)
        }
      })
    })
  }).catch(err => console.log('err', err) /* ||dispatch({ type: GET_CAMPAIGNS_PARTICIPANTS_FAILURE })  */)
}

/**
 * Redux Action Get Participant
 */
export const getCampaignParticipants = eventId => (dispatch, getState) => {
  dispatch({ type: GET_CAMPAIGN_PARTICIPANTS })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(participantsCollection)
      .doc(`${eventId}`)
      .get()
      .then(doc => {
        if (!doc.exists) {
          dispatch({ type: GET_CAMPAIGN_PARTICIPANTS_FAILURE })
          return reject({ message: langMessages[`${participantsCollection}.inexistsMsg`], type: DOC_DOESNT_EXIST })
        }
        dispatch({ type: GET_CAMPAIGN_PARTICIPANTS_SUCCESS, ID: eventId, payload: doc.data() })
        return resolve(doc.data())
      })
      .catch(function (error) {
        console.error(error)
        dispatch({ type: GET_CAMPAIGN_PARTICIPANTS_FAILURE })
      })
  }).catch(err => console.log('err', err) /* ||dispatch({ type: GET_CAMPAIGN_PARTICIPANTS_FAILURE }) */)
}
