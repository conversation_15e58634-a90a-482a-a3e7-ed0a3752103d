/**
 * Posts Actions
 */
import { FirestoreRef, createPost } from 'FirebaseRef'

// lang strings
import { langMessages } from '../lang'

import { useDispatch } from 'react-redux'

import * as ActionTypes from 'Actions/types'

import postModels from '../models'

import {
  DELETE_POST,
  DELETE_POST_FAILURE,
  DELETE_POST_SUCCESS,
  FIRESTORE_ERROR,
  GET_POST,
  GET_POSTS,
  GET_POSTS_FAILURE,
  GET_POSTS_SUCCESS,
  GET_POST_FAILURE,
  GET_POST_SUCCESS,
  LISTEN_COLLECTION_INDEX,
  LISTEN_COLLECTION_INDEX_FAILURE,
  LISTEN_COLLECTION_INDEX_SUCCESS,
  LISTEN_POST,
  LISTEN_POSTS,
  LISTEN_POSTS_FAILURE,
  LISTEN_POSTS_SUCCESS,
  LISTEN_POST_FAILURE,
  LISTEN_POST_SUCCESS,
  MODULE_LIMIT_REACHED,
  MODULE_NOT_AVAILABLE,
  SAVE_DOCUMENT_FIELDS,
  SAVE_DOCUMENT_FIELDS_FAILURE,
  SAVE_DOCUMENT_FIELDS_SUCCESS,
  SAVE_NEW_POST,
  SAVE_NEW_POST_FAILURE,
  SAVE_NEW_POST_SUCCESS,
  SAVE_POST,
  SAVE_POST_FAILURE,
  SAVE_POST_FIELDS,
  SAVE_POST_FIELDS_FAILURE,
  SAVE_POST_FIELDS_SUCCESS,
  SAVE_POST_SUCCESS,
  TRASH_POST,
  TRASH_POST_FAILURE,
  TRASH_POST_SUCCESS,
  UPDATE_POST,
  UPDATE_POSTS,
} from './types'

import moment from 'moment'

import { DEFAULT_LOCALE, DOC_DOESNT_EXIST, MOMENT_ISO, MOMENT_SHORT, TRASH_STATUS } from '../constants/AppConstants'

import { currentUserCan, isEmptyObject, localJSON, replaceUndefined, sessionJSON } from '../helpers/helpers'

import COLLECTIONS from '../constants/AppCollections'
import CONSTANTS from '../constants/AppConstants'

const { keywordsSubcollections, KEYWORDS_SUBCOLLECTIONS_MAP, KEYWORDS_SUBCOLLECTION_NAME, INDEX_ID, COUNTER_ID } = COLLECTIONS

/**
 * Redux Action Get Posts
 */

export const getPosts = (accountId, collections, queries, limit, lastPos, orderArgs, subcollection) => (dispatch, getState) => {


  queries = queries || []

  if (typeof collections === 'string') {
    collections = [collections]
  }
  collections.forEach(collection => {
    dispatch({ type: GET_POSTS, collection })
  })

  return new Promise((resolve, reject) => {
    if (!accountId) return resolve([])

    collections.forEach(collection => {

      let QueryRef = FirestoreRef.collection(collection);
      // if subcollection, get the docs from subcollection
      if (subcollection) QueryRef = QueryRef.doc(accountId).collection(subcollection)
      if (accountId !== 1) QueryRef = QueryRef.where('accountId', '==', `${accountId}`)


      Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))

      let orderBy = ''
      let order = ''

      if (orderArgs) {
        orderBy = typeof orderArgs === 'string' ? orderArgs : orderArgs.orderBy
        order = (orderArgs && orderArgs.order) || 'asc'
      }

      if (orderBy) {
        QueryRef = QueryRef.orderBy(orderBy, order)
        !!lastPos && (QueryRef = QueryRef.startAfter(lastPos))
      }

      !isNaN(limit) && limit && (QueryRef = QueryRef.limit(limit))

      QueryRef.get()
        .then(snapshot => {
          const posts = snapshot.docs
            .map(doc => doc.data())
            .map(post => {
              if (post.systemUpdate) delete post.systemUpdate
              return post
            })
          dispatch({ type: GET_POSTS_SUCCESS, collection, payload: posts })
          let dispatchKey = `GET_${collection.toUpperCase()}_SUCCESS`
          if (ActionTypes[dispatchKey]) dispatch({ type: dispatchKey, payload: posts })

          return resolve(posts)
        })
        .catch(function (error) {
          dispatch({
            type: GET_POSTS_FAILURE,
            collection,
            error: error.message,
          })
          dispatch({ type: FIRESTORE_ERROR, error })
        })
    })
  })
}

/**
 * Redux Action Get Posts From Keywords subcollection
 */
export const getPostsFromKeywords = (account, collection, queries, limit, lastPos) => (dispatch, getState) => {


  if (!COLLECTIONS.coreCollections.includes(collection)) {
    dispatch({
      type: GET_POSTS_FAILURE,
      collection,
      error: 'Tipo de post não suportado',
    })
    return
  }

  queries = queries || []

  const accountId = account.ID
  const orderBy = CONSTANTS.UPDATED_FIELD
  const order = 'desc'

  dispatch({ type: GET_POSTS, collection })

  return new Promise((resolve, reject) => {
    const isCustomKeywordsCollection = keywordsSubcollections.includes(collection)
    const keywordsCollection = KEYWORDS_SUBCOLLECTIONS_MAP[collection] || KEYWORDS_SUBCOLLECTION_NAME

    let QueryRef = FirestoreRef.collectionGroup(keywordsCollection)

    Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))

    if (!isCustomKeywordsCollection && queries.find(q => q[0] === CONSTANTS.COLLECTION_FIELD)) {
      QueryRef = QueryRef.where(CONSTANTS.COLLECTION_FIELD, '==', collection)
    }

    QueryRef = QueryRef.orderBy(orderBy, order)

    if (lastPos) QueryRef = QueryRef.startAfter(lastPos)

    const { allKeywordFields } = COLLECTIONS.KEYWORDS_FIELDS_MAP[collection]

    const fieldsMultiplier = queries.find(q => q[0] === CONSTANTS.KEYWORDS_FIELDKEY_FIELD) ? 1 : allKeywordFields.length
    !isNaN(limit) && (QueryRef = QueryRef.limit(limit * fieldsMultiplier))

    QueryRef.get()
      .then(async snapshot => {
        const docIds = []

        snapshot.forEach(doc => {
          let { docId } = doc.data()
          !docIds.includes(docId) && docIds.push(docId)
        })

        const promises = docIds.map(docId => FirestoreRef.collection(collection).doc(docId).get())

        return Promise.all(promises)
          .then(results => {

            // !!!!!!!!!!!!!!!!!!!! [BUG] !!!!!!!!!!!!!!!!!!!!
            // length is the result of query, in case
            // there is a doc that doesn't exit but has keywords
            const length = results.length
            if (results.filter(doc => !doc.exists).length) {
              let bugDocs = results.filter(doc => !doc.exists)
              console.log(
                '**** BUG > DOESNT EXIST!!! ****',
                bugDocs.map(doc => doc.id),
                bugDocs
              )
            }
            // !!!!!!!!!!!!!!!!!!!! [BUG] !!!!!!!!!!!!!!!!!!!!

            const posts = results
              .filter(doc => doc.exists)
              .map(doc => doc.data())
              .map(post => {
                if (post.systemUpdate) delete post.systemUpdate
                return post
              })
            const lastKey = posts.length ? window.jsonClone(posts).pop()[orderBy] : lastPos || ''

            dispatch({ type: GET_POSTS_SUCCESS, collection, payload: posts })

            let dispatchKey = `GET_${collection.toUpperCase()}_SUCCESS`
            if (ActionTypes[dispatchKey]) dispatch({ type: dispatchKey, payload: posts })

            return resolve({ posts, lastKey, length })
          })
          .catch(reject)
      })
      .catch(reject)
  }).catch(function (error) {
    console.error({ GET_POSTS_FAILURE, collection, error })
    dispatch({ type: GET_POSTS_FAILURE, collection, error: error.message })
    dispatch({ type: FIRESTORE_ERROR, error })
  })
}

/**
 * Redux Action Listen Posts
 */
export const listenPosts =
  (accountId, collections, listenerFn, queries, getLogs = false) =>
    (dispatch, getState) => {
      let getLogsHistory = async (collection, post) => {
        if (!post?.ID) return (post.logHistory = [])

        try {
          const logs = await FirestoreRef.collection(collection).doc(`${post.ID}`).collection('logs').get()
          post.logHistory = logs.docs.map(doc => doc.data())
        } catch (err) {
          console.log('getLogsHistory ======== ', err)
        }

        return post
      }

      if (typeof collections === 'string') {
        collections = [collections]
      }

      return new Promise(res => {
        if (!accountId) return res([])

        const refListeners = {}
        collections.forEach(collection => {
          dispatch({ type: LISTEN_POSTS, collection })

          let QueryRef = FirestoreRef.collection(collection).where('accountId', '==', `${accountId}`)
          Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))

          let promises = []
          const refListener = QueryRef.onSnapshot(
            snapshot => {
              const posts = snapshot.docs.map(doc => {
                let post = doc.data()
                if (post.systemUpdate) delete post.systemUpdate
                if (getLogs) promises.push(getLogsHistory(collection, post))
                return post
              })

              if (getLogs) {
                Promise.all(promises).then(posts => {
                  dispatch({
                    type: LISTEN_POSTS_SUCCESS,
                    collection,
                    payload: posts,
                  })
                  listenerFn && listenerFn({ posts, collection, refListener })
                })
              } else {
                dispatch({
                  type: LISTEN_POSTS_SUCCESS,
                  collection,
                  payload: posts,
                })
                listenerFn && listenerFn({ posts, collection, refListener })
              }
            },
            error => {
              console.error(LISTEN_POSTS_FAILURE, error)
              dispatch({ type: LISTEN_POSTS_FAILURE, collection })
            }
          )
          refListeners[collection] = refListener
        })
        return res(refListeners)
      })
    }
/**
 * Redux Action Listen Posts
 */
export const listenCollectionIndex = (collections, accountId) => (dispatch, getState) => {

  if (!accountId) return

  if (typeof collections === 'string') {
    collections = [collections]
  }

  collections.forEach(collection => {
    dispatch({ type: LISTEN_COLLECTION_INDEX, collection })
  })

  FirestoreRef.collection(COLLECTIONS.ACCOUNTS_COLLECTION_NAME)
    .doc(accountId)
    .collection(INDEX_ID)
    .doc(COUNTER_ID)
    .onSnapshot(
      doc => {
        collections.forEach(collection => {
          if (!doc.exists) {
            dispatch({ type: LISTEN_COLLECTION_INDEX_FAILURE, collection })
            return
          }
          let data = doc.data()
          let collectionIndex = data[collection] || 0
          dispatch({
            type: LISTEN_COLLECTION_INDEX_SUCCESS,
            collection,
            payload: collectionIndex,
          })
        })
      },
      error => {
        console.error(error)
        collections.forEach(collection => {
          dispatch({ type: LISTEN_COLLECTION_INDEX_FAILURE, collection })
        })
      }
    )
}

/**
 * Redux Action Get Post
 */
export const getPost = (collection, postId) => (dispatch, getState) => {
  dispatch({ type: GET_POST, collection })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(collection)
      .doc(`${postId}`)
      .get()
      .then(doc => {
        if (!doc.exists) {
          let errorMessage = langMessages[`${collection}.inexistsMsg`] || langMessages['errors.inexistsErrorMsg']
          dispatch({ type: GET_POST_FAILURE, collection, error: errorMessage })
          return reject({
            doc,
            collection,
            postId,
            error: 'Documento inexistente',
            message: errorMessage,
            type: DOC_DOESNT_EXIST,
          })
        }
        dispatch({ type: GET_POST_SUCCESS, collection, payload: doc.data() })
        return resolve(doc.data())
      })
      .catch(function (error) {
        console.error(GET_POST_FAILURE, error)
        dispatch({ type: GET_POST_FAILURE, collection, error: error.message })
        dispatch({ type: FIRESTORE_ERROR, error })
      })
  })
}

/**
 * Redux Action Listen Post
 */
export const listenPost =
  (collection, postId, listenerFn, getLogs = false) =>
    (dispatch, getState) => {
      function getLogsHistory(collection, id) {
        return FirestoreRef.collection(collection).doc(`${id}`).collection('logs').get()
      }

      function onSucess(resolve, post, refListener) {
        dispatch({ type: LISTEN_POST_SUCCESS, collection, payload: post })
        listenerFn && listenerFn({ post, collection, refListener })
        return resolve(post)
      }

      dispatch({ type: LISTEN_POST, collection })
      return new Promise((resolve, reject) => {
        const refListener = FirestoreRef.collection(collection)
          .doc(`${postId}`)
          .onSnapshot(
            doc => {
              if (!doc.exists) {
                dispatch({ type: LISTEN_POST_FAILURE, collection })
                return reject({
                  message: langMessages[`${collection}.inexistsMsg`] || langMessages['errors.inexistsErrorMsg'],
                  type: DOC_DOESNT_EXIST,
                })
              }
              let post = doc.data()
              if (getLogs) {
                getLogsHistory(collection, post.ID).then(snapshot => {
                  let newArray = snapshot.docs.map(doc => doc.data())
                  post.logHistory = newArray
                  return onSucess(resolve, post, refListener)
                })
              } else {
                return onSucess(resolve, post, refListener)
              }
            },
            error => {
              console.error(LISTEN_POST_FAILURE, error)
              dispatch({ type: LISTEN_POST_FAILURE, collection })
            }
          )
      })
    }

export const saveNewPost = (collection, data) => (dispatch, getState) => {
  return unconnectedSaveNewPost(collection, data, dispatch)
}
export const unconnectedSaveNewPost = (collection, data, dispatch) => {
  data = replaceUndefined(data)
  dispatch = dispatch || useDispatch()
  dispatch({ type: SAVE_NEW_POST, collection })
  return new Promise((resolve, reject) => {
    const user = localJSON.get('user', false)
    const currentOwnerID = sessionJSON.get('currentOwnerID', false)
    const ownerId = data.owner || currentOwnerID || user.owner
    const currentAccountID = sessionJSON.get('account', {}).ID || (user || {}).accountId
    const accountId = data.accountId || currentAccountID || user.accountId

    if (!accountId) {
      dispatch({ type: SAVE_NEW_POST_FAILURE, collection })
      return reject({ message: langMessages['errors.genericErrorMsg'] })
    }

    if (!currentUserCan('add', collection, user)) {
      dispatch({ type: SAVE_NEW_POST_FAILURE, collection })
      return reject({ message: langMessages['errors.permissionDenied'] })
    }

    const newPost = {
      ...data,
      title: data.title || langMessages[`modules.${collection}.singular`] + ' ' + moment().format(MOMENT_SHORT) || langMessages['texts.newPost'],
      date: moment().format(MOMENT_ISO),
      modified: moment().format(MOMENT_ISO),
      locale: data.locale || DEFAULT_LOCALE,
      collection,
      author: user.ID,
      owner: ownerId,
      accountId,
    }


    return createPost(newPost)
      .then(response => {
        let result = response.data
        if (result.error) {
          let errorMsg = langMessages['errors.genericErrorMsg']
          switch (result.error) {
            case MODULE_NOT_AVAILABLE:
              errorMsg = langMessages[`errors.${MODULE_NOT_AVAILABLE}`]
              break
            case MODULE_LIMIT_REACHED:
              errorMsg = langMessages[`placeholders.${MODULE_LIMIT_REACHED}`].replace('[%s]', langMessages[`modules.${collection}`] || collection)
              break
            default:
              break
          }
          dispatch({
            type: SAVE_NEW_POST_FAILURE,
            collection,
            error: result.error,
          })
          return reject({ message: errorMsg })
        } else if (result.collection === collection && result.ID) {
          const payload = result
          const updates = {}
          if (result.checklist) {
            updates.checklist = {
              ...result.checklist,
              ...(updates.checklist || {}),
              context: { collection, id: result.ID },
            }
          }
          if ((result.settings || {}).context) {
            updates.settings = {
              ...result.settings,
              ...(updates.settings || {}),
              context: { collection, id: result.ID },
            }
          }
          if (!isEmptyObject(updates)) {
            return FirestoreRef.collection(collection)
              .doc(result.ID)
              .update(updates)
              .then(r => {
                const updatedPost = { ...payload, ...updates }
                dispatch({
                  type: SAVE_NEW_POST_SUCCESS,
                  collection,
                  payload: updatedPost,
                })
                return resolve(updatedPost)
              })
          } else {
            dispatch({ type: SAVE_NEW_POST_SUCCESS, collection, payload })
            return resolve(payload)
          }
        }
      })
      .catch(function (error) {
        console.error(SAVE_NEW_POST_FAILURE, error)
        dispatch({
          type: SAVE_NEW_POST_FAILURE,
          collection,
          error: error.message,
        })
        dispatch({ type: FIRESTORE_ERROR, error })
        return reject({ message: langMessages['errors.dbErrorMsg'] })
      })
  })
}
export const savePost = (collection, data) => (dispatch, getState) => {
  data = replaceUndefined(data)
  dispatch({ type: SAVE_POST, collection })
  return new Promise((resolve, reject) => {
    const user = localJSON.get('user', false)
    const currentOwnerID = sessionJSON.get('currentOwnerID', false)
    const ownerId = data.owner || currentOwnerID || user.owner
    const currentAccountID = sessionJSON.get('account', {}).ID || (user || {}).accountId
    const accountId = data.accountId || currentAccountID || user.accountId

    const { ID } = data
    if (!ID) {
      dispatch({ type: SAVE_POST_FAILURE, collection })
      return reject({ message: langMessages['errors.genericErrorMsg'] })
    }

    const updatedPost = {
      ...data,
      title: data.title || langMessages[`modules.${collection}.singular`] + ' ' + ID || '',
      modified: moment().format(MOMENT_ISO),
      locale: data.locale || DEFAULT_LOCALE,
      collection,
      owner: ownerId,
      accountId,
      updatedBy: user.ID,
      logs: {
        ...(data.logs || {}),
        updated: {
          user: user.ID,
          operator_id: user.ID,
          date: moment().format(MOMENT_ISO),
        },
      },
    }

    FirestoreRef.collection(collection)
      .doc(ID)
      .update(updatedPost)
      .then(() => {
        const payload = updatedPost
        dispatch({ type: SAVE_POST_SUCCESS, collection, payload })
        return resolve(payload)
      })
      .catch(function (error) {
        dispatch({ type: SAVE_POST_FAILURE, collection, error: error.message })
        dispatch({ type: FIRESTORE_ERROR, error })
        return reject({ message: langMessages['errors.dbErrorMsg'] })
      })
  })
}

export const clonePost = (collection, post, postModel) => (dispatch, getState) => {
  const uniqueFields = [
    'ID',
    'id',
    'logs',
    'date',
    'modified',
    'createdAt',
    'updatedAt',
    'edit_url',
    'config_url',
    'post_id',
    'permalink',
    'viewlink',
    'shortlink',
  ]

  const user = localJSON.get('user', false)

  const collectionModel = postModels[collection] || {}

  let clonePost = {
    ...collectionModel,
    ...(postModel || {}),
    ...window.jsonClone(post),
    author: user.ID,
    updatedBy: user.ID,
  }

  uniqueFields.forEach(f => {
    if (clonePost[f]) clonePost[f] = ''
  })

  clonePost.title = `${post.title} - ${langMessages['texts.copy']}`
  clonePost.comments && (clonePost.comments = [])
  clonePost.attachments && (clonePost.attachments = [])
  clonePost.stats && (clonePost.stats = {})
  clonePost.views && (clonePost.views = 0)
  clonePost.deadline && (clonePost.deadline = { ...(post.deadline || {}), cronId: '' })

  if (clonePost.checklist) {
    clonePost.checklist = {
      ...clonePost.checklist,
      date: '',
      modified: '',
      deadline: { ...((post.checklist || {}).deadline || {}), cronId: '' },
      context: { collection, id: '' },
    }
    if (clonePost.checklist.tasks) {
      clonePost.checklist.tasks = post.checklist.tasks
        .filter(t => !t.deleted)
        .map(t => ({
          ...t,
          completed: false,
          deadline: { ...(t.deadline || {}), cronId: '' },
        }))
    }
  }

  if (clonePost.tasks) {
    if (Array.isArray(clonePost.tasks)) {
      clonePost.tasks = post.tasks
        .filter(t => !t.deleted)
        .map(t => ({
          ...t,
          completed: false,
          deadline: { ...(t.deadline || {}), cronId: '' },
        }))
    } else
      Object.keys(clonePost.tasks).forEach((tasklistId, i) => {
        const tasks = window.jsonClone(clonePost.tasks[tasklistId])
        clonePost.tasks[tasklistId] = tasks
          .filter(t => !t.deleted)
          .map(t => ({
            ...t,
            completed: false,
            deadline: { ...(t.deadline || {}), cronId: '' },
          }))
      })
  }

  switch (collection) {
    case COLLECTIONS.AUTOMATIONS_COLLECTION_NAME:
      clonePost.status = CONSTANTS.DRAFT_STATUS
      break
    case COLLECTIONS.LANDING_PAGES_COLLECTION_NAME:
      clonePost = {
        ...clonePost,
        config: {
          ...clonePost.config,
          domain: '',
          customDomain: false,
        },
      }
      break
    case COLLECTIONS.CAMPAIGNS_COLLECTION_NAME:
    case COLLECTIONS.CONTRACTS_COLLECTION_NAME:
    case COLLECTIONS.MAILING_COLLECTION_NAME:
    case COLLECTIONS.NOTIFICATIONS_COLLECTION_NAME:
      clonePost = {
        ...clonePost,
        status: CONSTANTS.DRAFT_STATUS,
        settings: {
          ...clonePost.settings,
          scheduled_date: '',
          context: { collection, id: '' },
        },
      }
      switch (collection) {
        case COLLECTIONS.CONTRACTS_COLLECTION_NAME:
          clonePost = {
            ...clonePost,
            data: { ...clonePost.data, contractNumber: '' },
            context: {},
          }
          break
        default:
          break
      }
      break
    default:
      break
  }

  return unconnectedSaveNewPost(collection, clonePost, dispatch)
}

export const savePostFields = (collection, postId, data) => (dispatch, getState) => {
  data = replaceUndefined(data)

  dispatch({ type: SAVE_POST_FIELDS, collection })
  return new Promise((resolve, reject) => {
    if (!postId) {
      dispatch({ type: SAVE_POST_FIELDS_FAILURE, collection })
      return reject({ message: langMessages['errors.genericErrorMsg'] })
    }

    const user = localJSON.get('user', false)

    const updatedPostData = {
      ...data,
      modified: moment().format(MOMENT_ISO),
      updatedBy: user.ID,
    }

    FirestoreRef.collection(collection)
      .doc(postId)
      .update(updatedPostData)
      .then(() => {
        const payload = updatedPostData
        dispatch({
          type: SAVE_POST_FIELDS_SUCCESS,
          collection,
          payload,
          postId,
        })
        return resolve(updatedPostData)
      })
      .catch(function (error) {
        dispatch({
          type: SAVE_POST_FIELDS_FAILURE,
          collection,
          error: error.message,
        })
        dispatch({ type: FIRESTORE_ERROR, error })
        return reject({ message: langMessages['errors.dbErrorMsg'] })
      })
  })
}

export const saveDocumentFields = (collectionPath, postId, data) => (dispatch, getState) => {
  data = replaceUndefined(data)

  dispatch({ type: SAVE_DOCUMENT_FIELDS, collectionPath })
  return new Promise((resolve, reject) => {
    if (!postId) {
      dispatch({ type: SAVE_DOCUMENT_FIELDS_FAILURE, collectionPath })
      return reject({ message: langMessages['errors.genericErrorMsg'] })
    }

    const updatedPostData = {
      ...data,
    }

    FirestoreRef.collection(collectionPath)
      .doc(postId)
      .update(updatedPostData)
      .then(() => {
        const payload = updatedPostData
        dispatch({
          type: SAVE_DOCUMENT_FIELDS_SUCCESS,
          collectionPath,
          payload,
          postId,
        })
        return resolve(updatedPostData)
      })
      .catch(function (error) {
        dispatch({
          type: SAVE_DOCUMENT_FIELDS_FAILURE,
          collectionPath,
          error: error.message,
        })
        dispatch({ type: FIRESTORE_ERROR, error })
        return reject({ message: langMessages['errors.dbErrorMsg'] })
      })
  })
}

export const onUpdatePosts = (collection, data) => ({
  type: UPDATE_POSTS,
  collection,
  payload: data,
})

export const onUpdatePost = (collection, data) => ({
  type: UPDATE_POST,
  collection,
  payload: data,
})

export const removePostFromState = (collection, postId) => ({
  type: DELETE_POST_SUCCESS,
  collection,
  ID: postId,
})

export const onTrashPost = (collection, postId) => (dispatch, getState) => {
  dispatch({ type: TRASH_POST })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(collection)
      .doc(`${postId}`)
      .update({ status: TRASH_STATUS })
      .then(() => {
        dispatch({ type: TRASH_POST_SUCCESS, collection, ID: postId })
        return resolve()
      })
      .catch(function (error) {
        dispatch({
          type: TRASH_POST_FAILURE,
          collection,
          ID: postId,
          error: error.message,
        })
        dispatch({ type: FIRESTORE_ERROR, error })
        return reject()
      })
  })
}

export const deletePost = (collection, postId) => (dispatch, getState) => {
  dispatch({ type: DELETE_POST })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(collection)
      .doc(`${postId}`)
      .delete()
      .then(() => {
        dispatch({ type: DELETE_POST_SUCCESS, collection, ID: postId })
        return resolve()
      })
      .catch(function (error) {
        dispatch({
          type: DELETE_POST_FAILURE,
          collection,
          ID: postId,
          error: error.message,
        })
        dispatch({ type: FIRESTORE_ERROR, error })
      })
  })
}
