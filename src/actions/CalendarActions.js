/**
 * CalendarEvents Actions
 */
import { FirestoreRef } from 'FirebaseRef'

import { langMessages } from '../lang'

import moment from 'moment'

import {
  ADD_CALENDAR_EVENT,
  DELETE_CALENDAR_EVENT,
  DELETE_CALENDAR_EVENT_FAILURE,
  DELETE_CALENDAR_EVENT_SUCCESS,
  GET_CALENDAR_EVENT,
  GET_CALENDAR_EVENT_FAILURE,
  GET_CALENDAR_EVENT_SUCCESS,
  GET_CALENDAR_EVENTS,
  GET_CALENDAR_EVENTS_FAILURE,
  GET_CALENDAR_EVENTS_SUCCESS,
  LISTEN_CALENDAR_EVENTS,
  LISTEN_CALENDAR_EVENTS_FAILURE,
  LISTEN_CALENDAR_EVENTS_SUCCESS,
  SAVE_CALENDAR_EVENT,
  SAVE_CALENDAR_EVENT_FAILURE,
  SAVE_CALENDAR_EVENT_SUCCESS,
  SAVE_NEW_CALENDAR_EVENT,
  SAVE_NEW_CALENDAR_EVENT_FAILURE,
  SAVE_NEW_CALENDAR_EVENT_SUCCESS,
  UPDATE_CALENDAR_EVENT,
  UPDATE_CALENDAR_EVENTS,
} from 'Actions/types'

import COLLECTIONS from '../constants/AppCollections'
import { DEFAULT_LOCALE, DOC_DOESNT_EXIST, MOMENT_ISO } from '../constants/AppConstants'
import { localJSON, sessionJSON } from '../helpers/helpers'

const collectionName = COLLECTIONS.CALENDAR_COLLECTION_NAME

/**
 * Redux Action Get CalendarEvents
 */
export const getCalendarEvents = (accountId, queries) => (dispatch, getState) => {
  dispatch({ type: GET_CALENDAR_EVENTS })
  return new Promise((resolve, reject) => {
    let QueryRef = FirestoreRef.collection(collectionName).where('accountId', '==', `${accountId}`)
    Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))
    QueryRef.get()
      .then(snapshot => {
        const posts = []
        snapshot.forEach(doc => {
          posts.push(doc.data())
        })
        dispatch({ type: GET_CALENDAR_EVENTS_SUCCESS, payload: posts })
        return resolve(posts)
      })
      .catch(function (error) {
        dispatch({ type: GET_CALENDAR_EVENTS_FAILURE })
      })
  })
}

/**
 * Redux Action Get CalendarEvents
 */
export const listenCalendarEvents = (accountId, listenerFn, queries) => (dispatch, getState) => {
  dispatch({ type: LISTEN_CALENDAR_EVENTS })

  let QueryRef = FirestoreRef.collection(collectionName).where('accountId', '==', `${accountId}`)
  Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))

  return new Promise(res => {
    const refListener = QueryRef.onSnapshot(
      snapshot => {
        const posts = snapshot.docs.map(doc => doc.data())
        dispatch({ type: LISTEN_CALENDAR_EVENTS_SUCCESS, payload: posts })
        !!listenerFn && listenerFn(posts, refListener)
      },
      error => {
        console.error('LISTEN_CALENDAR_EVENTS_FAILURE', error)
        dispatch({ type: LISTEN_CALENDAR_EVENTS_FAILURE })
      }
    )

    return res(refListener)
  })
}

/**
 * Redux Action Get CalendarEvent
 */
export const getCalendarEvent = eId => (dispatch, getState) => {
  dispatch({ type: GET_CALENDAR_EVENT })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(collectionName)
      .doc(`${eId}`)
      .get()
      .then(doc => {
        if (!doc.exists) {
          dispatch({ type: GET_CALENDAR_EVENT_FAILURE })
          return reject({ message: langMessages[`${collectionName}.inexistsMsg`], type: DOC_DOESNT_EXIST })
        }
        dispatch({ type: GET_CALENDAR_EVENT_SUCCESS, payload: doc.data() })
        return resolve(doc.data())
      })
      .catch(function (error) {
        dispatch({ type: GET_CALENDAR_EVENT_FAILURE })
      })
  })
}

export const saveNewCalendarEvent = data => (dispatch, getState) => {
  const user = localJSON.get('user', false)
  dispatch({ type: SAVE_NEW_CALENDAR_EVENT })
  return new Promise((resolve, reject) => {
    // owner
    const currentOwnerID = sessionJSON.get('currentOwnerID', false)
    const ownerId = data.owner || currentOwnerID || user.owner
    const currentAccountID = sessionJSON.get('account', {}).ID || (user || {}).accountId
    const accountId = data.accountId || currentAccountID || user.accountId
    // end owner

    if (!accountId) {
      dispatch({ type: SAVE_NEW_CALENDAR_EVENT_FAILURE })
      return reject()
    }

    const start = (data.start && moment(data.start).format(MOMENT_ISO)) || moment().format(MOMENT_ISO)
    const end = (data.end && moment(data.end).format(MOMENT_ISO)) || ''

    const newPost = {
      ...data,
      start,
      end,
      date: moment().format(MOMENT_ISO),
      modified: moment().format(MOMENT_ISO),
      locale: data.locale || DEFAULT_LOCALE,
      collection: collectionName,
      author: user.ID,
      owner: ownerId,
      accountId,
    }

    FirestoreRef.collection(collectionName)
      .add(newPost)
      .then(doc => {
        const payload = { ...newPost, id: doc.id, ID: doc.id }
        dispatch({ type: SAVE_NEW_CALENDAR_EVENT_SUCCESS, payload })
        return resolve(payload)
      })
      .catch(function (error) {
        dispatch({ type: SAVE_NEW_CALENDAR_EVENT_FAILURE })
        return reject()
      })
  })
}

export const saveCalendarEvent = (data, ID) => (dispatch, getState) => {
  dispatch({ type: SAVE_CALENDAR_EVENT })
  return new Promise((resolve, reject) => {
    const currentOwnerID = sessionJSON.get('currentOwnerID', false)
    const user = localJSON.get('user', false)
    const ownerId = currentOwnerID || user.owner
    const currentAccountID = sessionJSON.get('account', {}).ID || (user || {}).accountId
    const accountId = data.accountId || currentAccountID || user.accountId

    if (!data.owner) data.owner = ownerId
    if (!data.accountId) data.accountId = accountId

    const start = (data.start && moment(data.start).format(MOMENT_ISO)) || moment().format(MOMENT_ISO)
    const end = (data.end && moment(data.end).format(MOMENT_ISO)) || ''

    if (data.category && data.schedule_id) {
      FirestoreRef.collection(COLLECTIONS.SHOTX_CRON_COLLECTION_NAME)
        .doc(data.schedule_id)
        .update({ scheduled_date: start })
    }

    const updatedCalendarEvent = {
      ...data,
      start,
      end,
      modified: moment().format(MOMENT_ISO),
      locale: data.locale || DEFAULT_LOCALE,
      collection: collectionName,
      logs: {
        ...(data.logs || {}),
        updated: {
          user: user.ID,
          operator_id: user.ID,
          date: moment().format(MOMENT_ISO),
        },
      },
    }
    // console.log('saveCalendarEvent > updatedCalendarEvent',updatedCalendarEvent);
    FirestoreRef.collection(collectionName)
      .doc(ID)
      .update(updatedCalendarEvent)
      .then(() => {
        const payload = updatedCalendarEvent
        dispatch({ type: SAVE_CALENDAR_EVENT_SUCCESS, payload })
        return resolve(payload)
      })
      .catch(function (error) {
        dispatch({ type: SAVE_CALENDAR_EVENT_FAILURE })
        return reject()
      })
  })
}

export const deleteCalendarEvent = (eId, category, schedule_id) => (dispatch, getState) => {
  dispatch({ type: DELETE_CALENDAR_EVENT })
  return new Promise((resolve, reject) => {
    if (category && schedule_id) {
      FirestoreRef.collection(COLLECTIONS.SHOTX_CRON_COLLECTION_NAME)
        .doc(`${schedule_id}`)
        .delete()
    }
    FirestoreRef.collection(collectionName)
      .doc(`${eId}`)
      .delete()
      .then(() => {
        dispatch({ type: DELETE_CALENDAR_EVENT_SUCCESS, ID: eId })
        return resolve(eId)
      })
      .catch(function (error) {
        dispatch({ type: DELETE_CALENDAR_EVENT_FAILURE, ID: eId })
        return reject(error)
      })
  })
}

export const addCalendarEvent = data => ({
  type: ADD_CALENDAR_EVENT,
  payload: data,
})

export const onUpdateCalendarEvent = data => ({
  type: UPDATE_CALENDAR_EVENT,
  payload: data,
})

export const onUpdateCalendarEvents = data => ({
  type: UPDATE_CALENDAR_EVENTS,
  payload: data,
})
