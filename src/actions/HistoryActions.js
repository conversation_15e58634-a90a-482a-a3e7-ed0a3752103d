/**
 * History Actions
 */

import { FirebaseRepository } from 'FirebaseRef/repository'
import { HISTORIC_COLLECTION_NAME, STORIES_SUBCOLLECTION_NAME } from '../constants/AppCollections'

/**
 * Function listen History
 *
 * @param {string} accountId
 * @param {string} relation
 * @param {string} relationId
 * @param {function} callback
 */
export const listenHistory = (accountId, relation, relationId, callback, wheres, onErrorCallback) => {
  const path = `${HISTORIC_COLLECTION_NAME}/${accountId}/${STORIES_SUBCOLLECTION_NAME}`

  if (path.includes('undefined')) {
    console.error('path is invalid')
    console.error(path)
    return
  }

  const repository = new FirebaseRepository()

  const onSuccess = stories => {
    stories = stories.sort((a, b) => (a.timestamp - b.timestamp))
    callback && callback(stories)
  }

  const onError = err => {
    console.error(err)
    onErrorCallback && onErrorCallback([])
  }

  const where = [
    ['relation', '==', relation],
    ['relationId', '==', relationId],
    ...(wheres || []),
  ]

  return repository.listenCollection(path, onSuccess, onError, where)
}

export const saveStory = (accountId, story) => {
  const path = `${HISTORIC_COLLECTION_NAME}/${accountId}/${STORIES_SUBCOLLECTION_NAME}/`
  const repository = new FirebaseRepository()

  return repository.addDoc(path, story)
}
