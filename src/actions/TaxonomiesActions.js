/**
 * Taxonomies Actions
 */
import { FirestoreRef } from 'FirebaseRef'

import { langMessages } from '../lang'

import {
  DELETE_TAXONOMY,
  GET_TAXONOMIES,
  GET_TAXONOMIES_FAILURE,
  GET_TAXONOMIES_KEYWORDS,
  GET_TAXONOMIES_SUCCESS,
  GET_TAXONOMY,
  GET_TAXONOMY_FAILURE,
  GET_TAXONOMY_SUCCESS,
  LISTEN_TAXONOMIES,
  LISTEN_TAXONOMIES_FAILURE,
  LISTEN_TAXONOMIES_SUCCESS,
  SAVE_NEW_TAXONOMY,
  SAVE_NEW_TAXONOMY_FAILURE,
  SAVE_NEW_TAXONOMY_SUCCESS,
  SAVE_TAXONOMY,
  SAVE_TAXONOMY_FAILURE,
  SAVE_TAXONOMY_SUCCESS,
  UPDATE_TAXONOMIES,
  UPDATE_TAXONOMY,
} from 'Actions/types'

import moment from 'moment'
import { TAXONOMIES_COLLECTION_NAME } from '../constants/AppCollections'
import { DOC_DOESNT_EXIST, MOMENT_ISO } from '../constants/AppConstants'
import { OWNER_LEVEL } from '../constants/UsersRoles'
import { isLoopableObject, localJSON, removeSpaces, sessionJSON } from '../helpers/helpers'

/**
 * Redux Action Get Taxonomies
 */
export const getTaxonomies = (accountId, taxNames, collection, params, limit, lastPos, orderArgs) => (dispatch, getState) => {
  // console.log('getTaxonomies', { accountId, taxNames, collection, params, limit, lastPos, orderArgs });
  if (typeof taxNames === 'string') {
    taxNames = [taxNames]
  }
  taxNames.forEach(taxName => {
    dispatch({ type: GET_TAXONOMIES, taxName, collection })
  })
  return new Promise((resolve, reject) => {
    const queries = Array.isArray(params) ? params : []
    const qObj = isLoopableObject(params) ? params : {}

    taxNames.forEach(taxName => {
      let QueryRef = FirestoreRef.collection(TAXONOMIES_COLLECTION_NAME).doc(collection).collection(taxName).where('accountId', '==', `${accountId}`)

      if (Array.isArray(queries) && queries.length) {
        queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))
      } else if (qObj.searchTerm) {
        const keyword = removeSpaces(qObj.searchTerm)
        QueryRef = QueryRef.where('keywords', 'array-contains', `${keyword}`)

        if (!queries.length) {
          const keywords = [keyword]
          dispatch({ type: GET_TAXONOMIES_KEYWORDS, taxName, collection, keywords })
        }
      } else if (Array.isArray(qObj.keywords)) {
        const keywords = qObj.keywords.map(w => removeSpaces(w))
        QueryRef = QueryRef.where('keywords', 'array-contains-any', keywords)

        if (!queries.length) {
          dispatch({ type: GET_TAXONOMIES_KEYWORDS, taxName, collection, keywords })
        }
      }

      if (orderArgs) {
        let orderBy = ''
        let order = ''

        orderBy = typeof orderArgs === 'string' ? orderArgs : orderArgs.orderBy
        order = (orderArgs && orderArgs.order) || 'asc'

        if (orderBy) {
          QueryRef = QueryRef.orderBy(orderBy, order)
        }

        !!lastPos && (QueryRef = QueryRef.startAt(lastPos))
      }

      !isNaN(limit) && limit && (QueryRef = QueryRef.limit(limit))

      QueryRef.get()
        .then(snapshot => {
          const taxonomies = []
          snapshot.forEach(doc => {
            const docData = doc.data()
            FirestoreRef.collection(collection).where('accountId', '==', `${accountId}`).where('tags', 'array-contains', `${doc.id}`).get()
              .then(snapshotCount => {
                return resolve(docData[collection] = snapshotCount.size)
              })
            taxonomies.push(docData)
          })
          dispatch({ type: GET_TAXONOMIES_SUCCESS, taxName, collection, payload: taxonomies })
          return resolve(taxonomies)
        })
        .catch(function (error) {
          console.error(GET_TAXONOMIES_FAILURE, error)
          dispatch({ type: GET_TAXONOMIES_FAILURE, taxName, collection })
        })
    })
  })
}

/**
 * Redux Action Listen Taxonomies
 */
export const listenTaxonomies = (accountId, taxNames, collection, queries, listenerKey) => (dispatch, getState) => {
  if (typeof taxNames === 'string') {
    taxNames = [taxNames]
  }
  taxNames.forEach(taxName => {
    dispatch({ type: LISTEN_TAXONOMIES, taxName, collection })
  })
  return new Promise((resolve, reject) => {
    taxNames.forEach(taxName => {
      let QueryRef = FirestoreRef.collection(TAXONOMIES_COLLECTION_NAME).doc(collection).collection(taxName).where('accountId', '==', `${accountId}`)
      Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))
      const refListener = QueryRef.onSnapshot(
        snapshot => {
          const taxonomies = []
          snapshot.forEach(doc => {
            taxonomies.push(doc.data())
          })
          dispatch({ type: LISTEN_TAXONOMIES_SUCCESS, taxName, collection, payload: taxonomies })
          return resolve(taxonomies, taxName, collection, refListener)
        },
        error => {
          console.error(LISTEN_TAXONOMIES_FAILURE, error)
          dispatch({ type: LISTEN_TAXONOMIES_FAILURE, taxName, collection })
        }
      )
    })
  })
}

/**
 * Redux Action Get Taxonomy
 */
export const getTaxonomy = (taxName, collection, taxId) => (dispatch, getState) => {
  // console.log('getTaxonomy', { taxName, collection, taxId });
  dispatch({ type: GET_TAXONOMY, taxName, collection })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(TAXONOMIES_COLLECTION_NAME)
      .doc(collection)
      .collection(taxName)
      .doc(`${taxId}`)
      .get()
      .then(doc => {
        if (!doc.exists) {
          dispatch({ type: GET_TAXONOMY_FAILURE, taxName, collection })
          return reject({ message: langMessages[`${taxName}.inexistsMsg`], type: DOC_DOESNT_EXIST })
        }
        dispatch({ type: GET_TAXONOMY_SUCCESS, taxName, collection, payload: doc.data() })
        return resolve(doc.data())
      })
      .catch(function (error) {
        console.error(GET_TAXONOMY_FAILURE, error)
        dispatch({ type: GET_TAXONOMY_FAILURE, taxName, collection })
      })
  })
}

export const saveNewTaxonomy = (taxName, collection, data) => (dispatch, getState) => {
  dispatch({ type: SAVE_NEW_TAXONOMY, taxName, collection })
  return new Promise((resolve, reject) => {
    const user = localJSON.get('user', false)

    // owner
    const currentOwnerID = sessionJSON.get('currentOwnerID', false)
    const ownerId = data.owner || currentOwnerID || user.owner || (user.level <= OWNER_LEVEL && user.ID) || ''
    const currentAccountID = sessionJSON.get('account', {}).ID || (user || {}).accountId
    const accountId = data.accountId || currentAccountID || user.accountId
    // end owner

    if (!accountId) {
      dispatch({ type: SAVE_NEW_TAXONOMY_FAILURE, taxName, collection })
      return reject()
    }

    const newTaxonomy = {
      ...data,
      date: moment().format(MOMENT_ISO),
      modified: moment().format(MOMENT_ISO),
      taxName,
      collection,
      author: user.ID,
      owner: ownerId,
      accountId,
    }

    FirestoreRef.collection(TAXONOMIES_COLLECTION_NAME)
      .doc(collection)
      .collection(taxName)
      .add(newTaxonomy)
      .then(doc => {
        const payload = { ...newTaxonomy, id: doc.id, ID: doc.id }
        dispatch({ type: SAVE_NEW_TAXONOMY_SUCCESS, taxName, collection, payload })
        return resolve(payload, taxName, collection)
      })
      .catch(function (error) {
        console.error(SAVE_NEW_TAXONOMY_FAILURE, error)
        dispatch({ type: SAVE_NEW_TAXONOMY_FAILURE, taxName, collection })
        return reject()
      })
  })
}

export const saveTaxonomy = (taxName, collection, data) => (dispatch, getState) => {
  dispatch({ type: SAVE_TAXONOMY, taxName, collection })
  return new Promise((resolve, reject) => {
    const user = localJSON.get('user', false)
    const { ID } = data

    if (!ID) {
      dispatch({ type: SAVE_TAXONOMY_FAILURE, taxName, collection })
      return reject('SAVE_TAXONOMY_FAILURE: No ID provided')
    }

    // owner
    if (!data.owner) {
      const currentOwnerID = sessionJSON.get('currentOwnerID', false)
      const ownerId = currentOwnerID || user.owner || (user.level <= OWNER_LEVEL && user.ID) || ''
      data.owner = ownerId
    }
    if (!data.accountId) {
      const currentAccountID = sessionJSON.get('account', {}).ID || (user || {}).accountId
      const accountId = currentAccountID || user.accountId
      data.accountId = accountId
    }
    // end owner

    const updatedTaxonomy = {
      ...data,
      modified: moment().format(MOMENT_ISO),
      taxName,
      collection,
      logs: {
        ...(data.logs || {}),
        updated: {
          user: user.ID,
          operator_id: user.ID,
          date: moment().format(MOMENT_ISO),
        },
      },
    }

    // console.log('saveTaxonomy > updatedTaxonomy',updatedTaxonomy);
    FirestoreRef.collection(TAXONOMIES_COLLECTION_NAME)
      .doc(collection)
      .collection(taxName)
      .doc(ID)
      .update(updatedTaxonomy)
      .then(() => {
        const payload = updatedTaxonomy
        dispatch({ type: SAVE_TAXONOMY_SUCCESS, taxName, collection, payload })
        return resolve(payload, taxName, collection)
      })
      .catch(function (error) {
        dispatch({ type: SAVE_TAXONOMY_FAILURE, taxName, collection })
        return reject()
      })
  })
}

export const onUpdateTaxonomies = (taxName, collection, data) => ({
  type: UPDATE_TAXONOMIES,
  taxName,
  collection,
  payload: data,
})

export const onUpdateTaxonomy = (taxName, collection, data) => ({
  type: UPDATE_TAXONOMY,
  taxName,
  collection,
  payload: data,
})

export const deleteTaxonomy = (taxName, collection, data) => ({
  type: DELETE_TAXONOMY,
  taxName,
  collection,
  payload: data,
})

export const onTrashTaxonomy = (taxName, collection, taxId) => ({
  type: DELETE_TAXONOMY,
  taxName,
  collection,
  payload: taxId,
})
