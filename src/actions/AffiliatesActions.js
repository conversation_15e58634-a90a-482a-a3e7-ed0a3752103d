/**
 * Affiliates Actions
 */
import { FirestoreRef, createAffiliate, updateAffiliate } from '../firebase'

// lang strings
import { langMessages } from '../lang'

import {
  FIRESTORE_ERROR,
  GET_AFFILIATES,
  GET_AFFILIATES_SUCCESS,
  GET_AFFILIATES_FAILURE,
  GET_AFFILIATES_KEYWORDS,
  GET_AFFILIATE,
  GET_AFFILIATE_SUCCESS,
  GET_AFFILIATE_FAILURE,
  SAVE_NEW_AFFILIATE,
  SAVE_NEW_AFFILIATE_FAILURE,
  SAVE_NEW_AFFILIATE_SUCCESS,
  UPDATE_AFFILIATES,
  UPDATE_AFFILIATE,
  SAVE_AFFILIATE,
  SAVE_AFFILIATE_SUCCESS,
  SAVE_AFFILIATE_FAILURE,
  DELETE_AFFILIATE,
  DELETE_AFFILIATE_SUCCESS,
  DELETE_AFFILIATE_FAILURE,
  TRASH_AFFILIATE,
  TRASH_AFFILIATE_SUCCESS,
  TRASH_AFFILIATE_FAILURE,
  GET_AFFILIATE_ID,
  GET_AFFILIATE_ID_SUCCESS,
  GET_AFFILIATE_ID_FAILURE,
  GET_CURRENT_AFFILIATE,
  GET_CURRENT_AFFILIATE_SUCCESS,
  GET_CURRENT_AFFILIATE_FAILURE,
  UPDATE_AFFILIATE_ID,
} from 'Actions/types'

import moment from 'moment'

import { localJSON, sessionJSON, isLoopableObject, removeSpaces, currentUserCan } from 'Helpers/helpers'

import ERROR_TYPES from '../constants/AppErrors'

import { DEFAULT_LOCALE, MOMENT_ISO, TRASH_STATUS, DOC_DOESNT_EXIST } from '../constants/AppConstants'

import { AFFILIATES_COLLECTION_NAME } from '../constants/AppCollections'

import { ADMIN_LEVEL, AFFILIATE_ROLE } from '../constants/UsersRoles'

import AffiliateModel from 'Routes/affiliates/model'

const collectionName = AFFILIATES_COLLECTION_NAME

/**
 * Redux Action Get Affiliates
 */
export const getAffiliates = (paramsOwner, where, params, roles) => (dispatch, getState) => {
  dispatch({ type: GET_AFFILIATES })

  return new Promise((resolve, reject) => {
    roles && typeof roles === 'string' && (roles = [roles])

    const queries = Array.isArray(where) ? where : []
    const qObj = isLoopableObject(where) ? where : params || {}

    let QueryRef = FirestoreRef.collection(collectionName)

    Array.isArray(roles) && roles.forEach(role => (QueryRef = QueryRef.where('roles', 'array-contains', role)))
    Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))

    if (qObj.searchTerm) {
      const keyword = removeSpaces(qObj.searchTerm)
      QueryRef = QueryRef.where('keywords', 'array-contains', `${keyword}`)

      if (!queries.length) {
        const keywords = [keyword]
        dispatch({ type: GET_AFFILIATES_KEYWORDS, keywords })
      }
    } else if (Array.isArray(qObj.keywords)) {
      const keywords = qObj.keywords.map(w => removeSpaces(w))
      QueryRef = QueryRef.where('keywords', 'array-contains-any', keywords)

      if (!queries.length) {
        dispatch({ type: GET_AFFILIATES_KEYWORDS, keywords })
      }
    } else {
      if (qObj.orderBy) QueryRef = QueryRef.orderBy(qObj.orderBy)
    }

    if (qObj.limit) QueryRef = QueryRef.limit(qObj.limit)

    QueryRef.get()
      .then(snapshot => {
        const posts = []
        snapshot.forEach(doc => {
          posts.push(doc.data())
        })
        dispatch({ type: GET_AFFILIATES_SUCCESS, payload: posts })
        return resolve(posts)
      })
      .catch(error => {
        console.error(error)
        dispatch({ type: GET_AFFILIATES_FAILURE })
        dispatch({ type: FIRESTORE_ERROR, error })
        return reject({ message: langMessages['errors.dbErrorMsg'] })
      })
  })
}

/**
 * Redux Action Get Affiliate
 */
export const getAffiliate = affiliateId => (dispatch, getState) => {
  dispatch({ type: GET_AFFILIATE })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(collectionName)
      .doc(`${affiliateId}`)
      .get()
      .then(doc => {
        if (!doc.exists) {
          dispatch({ type: GET_AFFILIATE_FAILURE })
          return reject({ message: langMessages[`${collectionName}.inexistsMsg`], type: DOC_DOESNT_EXIST })
        }
        dispatch({ type: GET_AFFILIATE_SUCCESS, payload: doc.data() })
        return resolve(doc.data())
      })
      .catch(error => {
        console.error(error)
        dispatch({ type: GET_AFFILIATE_FAILURE })
        dispatch({ type: FIRESTORE_ERROR, error })
        return reject({ message: langMessages['errors.dbErrorMsg'] })
      })
  })
}

/**
 * Redux Action Get Current Affiliate
 */
export const getCurrentAffiliate = () => (dispatch, getState) => {
  const user = localJSON.get('user', false)
  dispatch({ type: GET_CURRENT_AFFILIATE })
  const userId = user.ID
  return new Promise((resolve, reject) => {
    if (!userId) {
      dispatch({ type: GET_CURRENT_AFFILIATE_FAILURE })
      return reject({ message: langMessages['errors.genericErrorMsg'] })
    }
    if (user.ID === userId) {
      dispatch({ type: GET_CURRENT_AFFILIATE_SUCCESS, payload: user })
      return resolve(user)
    }
    FirestoreRef.collection(collectionName)
      .doc(`${userId}`)
      .get()
      .then(doc => {
        if (!doc.exists) {
          dispatch({ type: GET_CURRENT_AFFILIATE_FAILURE })
          return reject({ message: langMessages[`${collectionName}.inexistsMsg`], type: DOC_DOESNT_EXIST })
        }
        dispatch({ type: GET_CURRENT_AFFILIATE_SUCCESS, payload: doc.data() })
        return resolve(doc.data())
      })
      .catch(error => {
        dispatch({ type: GET_CURRENT_AFFILIATE_FAILURE })
        dispatch({ type: FIRESTORE_ERROR, error })
        return reject({ message: langMessages['errors.dbErrorMsg'] })
      })
  })
}

/**
 * Redux Action Get Current Affiliate
 */
export const getCurrentAffiliateID = () => (dispatch, getState) => {
  const user = localJSON.get('user', false)
  dispatch({ type: GET_AFFILIATE_ID })
  return new Promise((resolve, reject) => {
    const CurrentAffiliateID = sessionJSON.get('CurrentAffiliateID', false)
    const userId = CurrentAffiliateID || user.ID

    if (!userId) {
      dispatch({ type: GET_AFFILIATE_ID_FAILURE })
      return reject({ message: langMessages['errors.genericErrorMsg'] })
    }

    // dispatch anyway to speed up getting user
    dispatch({ type: GET_AFFILIATE_ID_SUCCESS, payload: userId })
    resolve(userId)

    if (userId !== user.ID && (!user.level || user.level > ADMIN_LEVEL)) {
      // Check if user exists in database
      FirestoreRef.collection(collectionName)
        .doc(`${userId}`)
        .get()
        .then(doc => {
          if (!doc.exists) {
            dispatch({ type: GET_AFFILIATE_ID_FAILURE })
            return reject({ message: langMessages[`${collectionName}.inexistsMsg`], type: DOC_DOESNT_EXIST })
          }
        })
        .catch(error => {
          dispatch({ type: GET_AFFILIATE_ID_FAILURE })
          dispatch({ type: FIRESTORE_ERROR, error })
        })
    }
  })
}

export const saveNewAffiliate = data => (dispatch, getState) => {
  dispatch({ type: SAVE_NEW_AFFILIATE })
  return new Promise((resolve, reject) => {
    const user = localJSON.get('user', false)

    if (!currentUserCan('add', collectionName, user)) {
      let error = langMessages['errors.permissionDenied']
      dispatch({ type: SAVE_NEW_AFFILIATE_FAILURE, error })
      return reject({ message: error })
    }

    const newAffiliate = sanitizeAffiliate(data, 'add')

    if (!newAffiliate.email) {
      let error = langMessages['alerts.emptyEmailAddress']
      dispatch({ type: SAVE_NEW_AFFILIATE_FAILURE, error })
      return reject({ message: error })
    }

    return createAffiliate(newAffiliate)
      .then(response => {
        // console.log('response',response);
        let result = response.data
        if (result.error) {
          let errorMsg = langMessages['errors.genericErrorMsg']
          switch (result.error) {
            case ERROR_TYPES.EMAIL_EXISTS_ERROR:
              errorMsg = langMessages['alerts.emailAlreadyExists']
              break
            case ERROR_TYPES.NEW_AUTH_USER_ERROR:
              if (result.code) {
                switch (result.code) {
                  case 'auth/invalid-phone-number':
                    errorMsg = langMessages['alerts.informFullPhoneNumber']
                    break
                  case 'auth/invalid-password':
                    errorMsg = langMessages['alerts.invalidPassword']
                    break
                  default:
                    errorMsg = langMessages['errors.genericErrorMsg']
                    break
                }
              }
              break
            case ERROR_TYPES.MISSING_REQUIRED_FIELDS:
              errorMsg = langMessages['errors.genericErrorMsg']
              break
            case ERROR_TYPES.PAGARME_CREATE_RECIPIENT_ERROR:
              errorMsg = langMessages['errors.integrationErrorMsg']
              break
            default:
              break
          }
          dispatch({ type: SAVE_NEW_AFFILIATE_FAILURE, error: result.error })
          return reject({ message: errorMsg })
        } else if (result.collection === collectionName && result.ID) {
          const payload = result
          dispatch({ type: SAVE_NEW_AFFILIATE_SUCCESS, payload })
          return resolve(payload)
        }
      })
      .catch(function (error) {
        console.error(SAVE_NEW_AFFILIATE_FAILURE, error)
        dispatch({ type: SAVE_NEW_AFFILIATE_FAILURE, error: error.message })
        dispatch({ type: FIRESTORE_ERROR, error })
        return reject({ message: langMessages['errors.dbErrorMsg'] })
      })
  })
}

export const saveAffiliate = data => (dispatch, getState) => {
  const { ID } = data
  dispatch({ type: SAVE_AFFILIATE })
  return new Promise((resolve, reject) => {
    if (!ID) {
      dispatch({ type: SAVE_AFFILIATE_FAILURE })
      return reject({ message: langMessages['errors.dbErrorMsg'] })
    }

    if (!data.email) {
      let error = langMessages['alerts.emptyEmailAddress']
      dispatch({ type: SAVE_NEW_AFFILIATE_FAILURE, error })
      return reject({ message: error })
    }

    const affiliate = sanitizeAffiliate(data, 'update')
    // console.log('saveAffiliate > affiliate',affiliate);

    return updateAffiliate(affiliate)
      .then(response => {
        // console.log('response',response);
        let result = response.data
        if (result.error) {
          let errorMsg = langMessages['errors.genericErrorMsg']
          switch (result.error) {
            case ERROR_TYPES.MISSING_REQUIRED_FIELDS:
              errorMsg = langMessages['errors.genericErrorMsg']
              break
            case ERROR_TYPES.PAGARME_UPDATE_RECIPIENT_ERROR:
              errorMsg = langMessages['errors.integrationErrorMsg']
              break
            default:
              break
          }
          dispatch({ type: SAVE_AFFILIATE_FAILURE, error: result.error })
          return reject({ message: errorMsg })
        } else if (result.collection === collectionName && result.ID) {
          const payload = result
          dispatch({ type: SAVE_AFFILIATE_SUCCESS, payload })
          return resolve(payload)
        }
      })
      .catch(function (error) {
        console.error(SAVE_NEW_AFFILIATE_FAILURE, error)
        dispatch({ type: SAVE_NEW_AFFILIATE_FAILURE, error: error.message })
        dispatch({ type: FIRESTORE_ERROR, error })
        return reject({ message: langMessages['errors.dbErrorMsg'] })
      })
  })
}

export const onTrashAffiliate = affiliateId => (dispatch, getState) => {
  const user = localJSON.get('user', false)
  if (user.ID === affiliateId) {
    return false
  }
  dispatch({ type: TRASH_AFFILIATE })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(collectionName)
      .doc(`${affiliateId}`)
      .get()
      .then(doc => {
        if (doc.exists) {
          let affiliate = doc.data()
          if (user.level < affiliate.level) {
            return doc.ref.update({ status: TRASH_STATUS }).then(() => {
              dispatch({ type: TRASH_AFFILIATE_SUCCESS, ID: affiliateId })
              return resolve(affiliateId)
            })
          }
        }
        dispatch({ type: TRASH_AFFILIATE_FAILURE, ID: affiliateId })
        return reject({ message: langMessages['errors.genericErrorMsg'] })
      })
      .catch(function (error) {
        console.error(error)
        dispatch({ type: TRASH_AFFILIATE_FAILURE, ID: affiliateId })
        return reject({ message: langMessages['errors.dbErrorMsg'] })
      })
  })
}

export const deleteAffiliate = affiliateId => (dispatch, getState) => {
  const user = localJSON.get('user', false)
  if (user.ID === affiliateId) {
    return false
  }
  dispatch({ type: DELETE_AFFILIATE })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(collectionName)
      .doc(`${affiliateId}`)
      .get()
      .then(doc => {
        if (doc.exists) {
          let affiliate = doc.data()
          if (user.level < affiliate.level) {
            return doc.ref.delete().then(() => {
              dispatch({ type: DELETE_AFFILIATE_SUCCESS, ID: affiliateId })
              return resolve(affiliateId)
            })
          }
        }
        dispatch({ type: DELETE_AFFILIATE_FAILURE, ID: affiliateId })
        return reject({ message: langMessages['errors.genericErrorMsg'] })
      })
      .catch(function (error) {
        console.error(error)
        dispatch({ type: DELETE_AFFILIATE_FAILURE, ID: affiliateId })
        return reject({ message: langMessages['errors.dbErrorMsg'] })
      })
  })
}

export const onUpdateAffiliate = (data, ID) => ({
  type: UPDATE_AFFILIATE,
  payload: data,
})

export const onUpdateAffiliates = data => ({
  type: UPDATE_AFFILIATES,
  payload: data,
})

export const onUpdateAffiliateID = data => {
  sessionJSON.set('CurrentAffiliateID', data)
  return {
    type: UPDATE_AFFILIATE_ID,
    payload: data,
  }
}

export const sanitizeAffiliate = (data, prepare) => {
  let { roles } = data

  roles = (Array.isArray(roles) && roles.length && [...new Set([...roles, AFFILIATE_ROLE])]) || [AFFILIATE_ROLE]

  const user = localJSON.get('user', false)

  // owner
  const currentAccountID = sessionJSON.get('account', {}).ID || (user || {}).accountId
  const accountId = data.accountId || currentAccountID || user.accountId
  // end owner

  let dbData = {
    ...AffiliateModel,
    ...window.jsonClone(data),
    collection: collectionName,
    locale: data.locale || DEFAULT_LOCALE,
    roles,
    accountId,
  }

  switch (prepare) {
    case 'add':
      dbData = {
        ...dbData,
        date: moment().format(MOMENT_ISO),
        modified: moment().format(MOMENT_ISO),
        author: user.ID,
        logs: {
          ...(data.logs || {}),
          added: {
            user: user.ID,
            operator_id: user.ID,
            date: moment().format(MOMENT_ISO),
          },
        },
      }

      break

    case 'update':
    default:
      dbData = {
        ...dbData,
        modified: moment().format(MOMENT_ISO),
        logs: {
          ...(data.logs || {}),
          updated: {
            user: user.ID,
            operator_id: user.ID,
            date: moment().format(MOMENT_ISO),
          },
        },
      }

      break
  }

  return dbData
}
