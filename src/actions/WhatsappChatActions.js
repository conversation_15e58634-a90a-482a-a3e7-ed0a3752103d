/* eslint-disable no-undef */
/**
 * Whatsapp Chat Actions
 */

import { FirebaseRepository } from 'FirebaseRef/repository'
import {
  CONTACTS_SUBCOLLECTION_NAME,
  INSTANCES_COLLECTION_NAME,
  LEADS_COLLECTION_NAME,
  MESSAGES_SUBCOLLECTION_NAME,
  SHOTX_COLLECTION_NAME,
} from '../constants/AppCollections'

/**
 * Function listen User Messages
 *
 * @param {string} accountId
 * @param {string} contactId
 * @param {function} callback
 */
export const listenUserMessages = (accountId, instanceID, contactId, callback) => {
  const path = `${SHOTX_COLLECTION_NAME}/${accountId}/${INSTANCES_COLLECTION_NAME}/${instanceID}/${CONTACTS_SUBCOLLECTION_NAME}/${contactId}/${MESSAGES_SUBCOLLECTION_NAME}`

  if (path.includes('undefined')) {
    console.error('path is invalid')
    console.error(path)
    return
  }

  const repository = new FirebaseRepository()

  const onSuccess = chats => {
    callback && callback(chats)
  }

  const onError = err => {
    console.error(err)
    callback && callback([])
  }

  return repository.listenCollection(path, onSuccess, onError)
}

export const fetchContact = (leadId, callback) => {
  if (!leadId) Error('leadId is required')

  const repository = new FirebaseRepository()

  return new Promise(res => {
    const path = `${LEADS_COLLECTION_NAME}/${leadId}`
    repository.getDoc(
      path,
      leads => {
        callback && callback(leads)
        res(leads)
      },
      err => {
        console.error(err)
      }
    )
  })
}

export const contactExists = (accountId, instanceID, contactID) => {
  const repository = new FirebaseRepository()

  return new Promise(res => {
    const path = `${SHOTX_COLLECTION_NAME}/${accountId}/${INSTANCES_COLLECTION_NAME}/${instanceID}/${CONTACTS_SUBCOLLECTION_NAME}/${contactID}/${MESSAGES_SUBCOLLECTION_NAME}`

    res(repository.collectionExists(path))
  })
}
