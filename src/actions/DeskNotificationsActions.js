/**
 * DeskNotifications Actions
 */
import { FirestoreRef } from '../firebase'

import {
  TOGGLE_DESK_NOTIFICATIONS,
  GET_DESK_NOTIFICATIONS,
  GET_DESK_NOTIFICATIONS_SUCCESS,
  GET_DESK_NOTIFICATIONS_FAILURE,
  LISTEN_DESK_NOTIFICATIONS,
  LISTEN_DESK_NOTIFICATIONS_SUCCESS,
  LISTEN_DESK_NOTIFICATIONS_FAILURE,
  UPDATE_DESK_NOTIFICATION,
  UPDATE_DESK_NOTIFICATION_SUCCESS,
  UPDATE_DESK_NOTIFICATION_FAILURE,
  UPDATE_DESK_NOTIFICATIONS,
  UPDATE_DESK_NOTIFICATIONS_SUCCESS,
  UPDATE_DESK_NOTIFICATIONS_FAILURE,
  DELETE_DESK_NOTIFICATION,
  DELETE_DESK_NOTIFICATION_SUCCESS,
  DELETE_DESK_NOTIFICATION_FAILURE,
} from 'Actions/types'

import moment from 'moment'
import { DEFAULT_LOCALE, MOMENT_ISO } from 'Constants'
import { DE<PERSON><PERSON>TOP_NOTIFICATIONS_COLLECTION_NAME } from 'Constants'
import { sessionJSON, localJSON } from 'Helpers/helpers'

const collectionName = DESKTOP_NOTIFICATIONS_COLLECTION_NAME

/**
 * Redux Action Get DeskNotifications
 */
export const getDeskNotifications = queries => (dispatch, getState) => {
  const user = localJSON.get('user', false)
  dispatch({ type: GET_DESK_NOTIFICATIONS })
  return new Promise((resolve, reject) => {
    let QueryRef = FirestoreRef.collection(collectionName).where('qiuser', '==', `${user.ID}`)
    Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))
    QueryRef.get()
      .then(snapshot => {
        const posts = []
        snapshot.forEach(doc => {
          posts.push(doc.data())
        })
        dispatch({ type: GET_DESK_NOTIFICATIONS_SUCCESS, payload: posts })
        return resolve(posts)
      })
      .catch(function (error) {
        dispatch({ type: GET_DESK_NOTIFICATIONS_FAILURE })
      })
  })
}

/**
 * Redux Action Get DeskNotifications
 */
export const listenDeskNotifications = (listenerFn, queries) => (dispatch, getState) => {
  const user = localJSON.get('user', false)
  dispatch({ type: LISTEN_DESK_NOTIFICATIONS })

  let QueryRef = FirestoreRef.collection(collectionName).where('qiuser', '==', `${user.ID}`)
  Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))

  return new Promise(res => {
    const refListener = QueryRef.onSnapshot(
      snapshot => {
        const posts = []
        snapshot.forEach(doc => {
          let d = doc.data()
          !!d.ID && posts.push(d)
        })
        dispatch({ type: LISTEN_DESK_NOTIFICATIONS_SUCCESS, payload: posts })
        if (posts.find(p => !p.viewed)) {
          dispatch({ type: TOGGLE_DESK_NOTIFICATIONS, payload: true })
        }
        listenerFn && listenerFn(posts, refListener)
      },
      error => {
        console.error('LISTEN_DESK_NOTIFICATIONS_FAILURE', error)
        dispatch({ type: LISTEN_DESK_NOTIFICATIONS_FAILURE })
      }
    )

    return res(refListener)
  })
}

export const updateDeskNotification = (data, ID) => (dispatch, getState) => {
  const user = localJSON.get('user', false)
  dispatch({ type: UPDATE_DESK_NOTIFICATION })
  return new Promise((resolve, reject) => {
    if (!data.qiuser) data.qiuser = user.ID
    if (!data.owner) data.owner = user.ID

    const updatedDeskNotification = {
      ...data,
      modified: moment().format(MOMENT_ISO),
      locale: data.locale || DEFAULT_LOCALE,
      collection: collectionName,
    }
    // console.log('updateDeskNotification > updatedDeskNotification',updatedDeskNotification);
    FirestoreRef.collection(collectionName)
      .doc(ID)
      .update(updatedDeskNotification)
      .then(() => {
        const payload = updatedDeskNotification
        dispatch({ type: UPDATE_DESK_NOTIFICATION_SUCCESS, payload })
        return resolve(payload)
      })
      .catch(function (error) {
        dispatch({ type: UPDATE_DESK_NOTIFICATION_FAILURE })
        return reject()
      })
  })
}

export const updateDeskNotifications = (deskNotifications, data) => (dispatch, getState) => {
  dispatch({ type: UPDATE_DESK_NOTIFICATIONS })
  return new Promise((resolve, reject) => {
    let updatedData = deskNotifications.map(n => ({
      ...(data || {}),
      ID: n.ID,
      modified: moment().format(MOMENT_ISO),
      locale: n.locale || DEFAULT_LOCALE,
      collection: collectionName,
    }))

    const updatedPosts = deskNotifications.map((n, i) => ({
      ...n,
      ...updatedData[i],
    }))

    if (updatedData.length) {
      const batch = FirestoreRef.batch()

      updatedData.forEach(d => {
        let docRef = FirestoreRef.collection(collectionName).doc(d.ID)
        batch.update(docRef, d)
      })

      return batch
        .commit()
        .then(() => {
          const payload = updatedPosts
          dispatch({ type: UPDATE_DESK_NOTIFICATIONS_SUCCESS, payload })
          return resolve(payload)
        })
        .catch(function (error) {
          dispatch({ type: UPDATE_DESK_NOTIFICATIONS_FAILURE })
          return reject()
        })
    }

    return resolve(deskNotifications)
  })
}

export const deleteDeskNotification = nId => (dispatch, getState) => {
  dispatch({ type: DELETE_DESK_NOTIFICATION })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(collectionName)
      .doc(`${nId}`)
      .delete()
      .then(() => {
        dispatch({ type: DELETE_DESK_NOTIFICATION_SUCCESS, ID: nId })
        return resolve(nId)
      })
      .catch(function (error) {
        dispatch({ type: DELETE_DESK_NOTIFICATION_FAILURE, ID: nId })
        return reject(error)
      })
  })
}
