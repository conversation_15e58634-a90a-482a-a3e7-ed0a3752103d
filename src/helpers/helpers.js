/**
 * Helpers Functions
 */

import { default as Axios, default as axios } from 'axios'
import $ from 'jquery'
import moment from 'moment'
import COLLECTIONS from '../constants/AppCollections'
import CONSTANTS, {
  APP_ENVIROINMENT,
  APP_TRIGGERS,
  APP_URL,
  MOMENT_ISO,
  QIPLUS_APP_ORIGIN,
  SITE_URL,
  URL_PARAMS_AFFILIATE_HOME_ID,
  URL_PARAMS_AFFILIATE_ID,
  URL_PARAMS_PARENT_ID,
  WP_AJAX_URL,
  qiplusPlansRoutes,
} from '../constants/AppConstants'
import { getCountries } from './ibgeHelper'

console.log(`
////////////////////////////////////////////////////////////

////////    //    ////////    //         //    //   ////////
//    //    //    //    //    //         //    //   //
//    //    //    ////////    //         //    //   ////////
//    //    //    //          //         //    //         //
////////    //    //          ////////   ////////   ////////
    \\\\
////////////////////////////////////////////////////////////

`)

export const localJSON = new DharmaStorage('localStorage')
export const sessionJSON = new DharmaStorage('sessionStorage')

if (APP_ENVIROINMENT !== 'development' && window.location.hostname !== 'localhost') {
  if (localJSON.get('user', {}).level !== 1) {
    console.log = () => { }
    console.group = () => { }
    console.groupCollapsed = () => { }
    console.groupEnd = () => { }
  }
}

let AppModules
/**
 * Function to convert hex to rgba
 */
export function hexToRgbA(hex, alpha) {
  var c
  if (/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)) {
    c = hex.substring(1).split('')
    if (c.length === 3) {
      c = [c[0], c[0], c[1], c[1], c[2], c[2]]
    }
    c = '0x' + c.join('')
    return 'rgba(' + [(c >> 16) & 255, (c >> 8) & 255, c & 255].join(',') + ',' + alpha + ')'
  }
  console.error('hex', { hex, alpha })
  throw new Error('Bad Hex')
}

/**
 * Text Truncate
 */
export function textTruncate(str, length, ending) {
  if (length == null) {
    length = 100
  }
  if (ending == null) {
    ending = '...'
  }
  if (str.length > length) {
    return str.substring(0, length - ending.length) + ending
  } else {
    return str
  }
}

/**
 * HTML Truncate
 */
export function htmlTruncate(html, length, ending) {
  if (length == null) {
    length = 100
  }
  if (ending == null) {
    ending = '...'
  }
  let div = $('<div/>').html((html || '').replace(/<img[^>]*>/g, ''))
  let str = div.text()
  if (str.length > length) {
    return str.substring(0, length - ending.length) + ending
  } else {
    return str
  }
}

/**
 * Get Date
 */
export function getTheDate(timestamp, format) {
  let time = timestamp * 1000
  let formatDate = format ? format : 'MM-DD-YYYY'
  return moment(time).format(formatDate)
}

export function getTheDateRefact(timestamp, format) {
  //let time = timestamp * 1000
  let formatDate = format ? format : 'MM-DD-YYYY'
  return moment(timestamp).format(formatDate)
}

/**
 * Convert Date To Timestamp
 */
export function convertDateToTimeStamp(date, format) {
  let formatDate = format ? format : CONSTANTS.MOMENT_ISO_DAY
  return moment(date, formatDate).unix()
}

// grabHereToCopyPaste
export const momentNow = d => moment(d) /*.utcOffset(CONSTANTS.MOMENT_BR_OFFSET);*/

// grabHereToCopyPaste
export const nowToISO = () => momentNow().format(CONSTANTS.MOMENT_ISO)

/**
 * Function to return current app layout
 */
export function getAppLayout(location) {
  let { pathname } = location
  let path = pathname.split('/')
  return path[1]
}

export function onlyNumbers(numbers) {
  numbers = numbers.replace(/\D/g, '')
  return numbers
}

export const getScheduledMoment = (n, data) => {
  let scheduled_date
  let { qty, interval } = n || {}
  if (n && n.type) {
    switch (n.type) {
      case 'timestamp':
        scheduled_date = n.time && moment(n.time).isValid() ? moment(n.time) : ''
        break
      case 'before':
        var start = data.start || data.date
        scheduled_date = qty && start && moment(start).isValid() ? moment(start) : ''
        if (!isNaN(parseInt(qty, 10)) && scheduled_date) {
          switch (interval) {
            case 'dd':
              scheduled_date.subtract(parseInt(qty, 10), 'days')
              break
            case 'hh':
              scheduled_date.subtract(parseInt(qty, 10), 'hours')
              break
            case 'mm':
              scheduled_date.subtract(parseInt(qty, 10), 'minutes')
              break
            default:
              break
          }
        }
        break
      case 'oncreate':
        var date = data.date
        scheduled_date = qty && date && moment(date).isValid() ? moment(date) : ''
        if (!isNaN(parseInt(qty, 10)) && scheduled_date) {
          switch (interval) {
            case 'dd':
              scheduled_date.add(parseInt(qty, 10), 'days')
              break
            case 'hh':
              scheduled_date.add(parseInt(qty, 10), 'hours')
              break
            case 'mm':
              scheduled_date.add(parseInt(qty, 10), 'minutes')
              break
            default:
              break
          }
        }
        break
      default:
        break
    }
  }
  return scheduled_date
}

export const parseRange = range => {
  let d = new Date()
  let start = moment().subtract(1, 'month').valueOf()
  let end = moment().valueOf()
  let days = replaceAll(['last', 'Days'], '', range)
  let Y = moment(d).format('Y')
  let MM = moment(d).format('MM')
  let DD = moment(d).format('DD')
  let HMS = '00:00:01'

  switch (range) {
    case 'today':
      start = moment(`${Y}-${MM}-${DD} ${HMS}`).valueOf()
      break
    case 'currentWeek':
      start = moment(`${Y}-${MM}-${DD} ${HMS}`)
        .subtract((d.getDay() || 7) - 1, 'days')
        .valueOf()
      break
    case 'currentMonth':
      start = moment(`${Y}-${MM}-01 ${HMS}`).valueOf()
      break
    case 'currentYear':
      start = moment(`${Y}-01-01 ${HMS}`).valueOf()
      break
    case 'last7Days':
    case 'last15Days':
    case 'last30Days':
    case 'last90Days':
    case 'last365Days':
      start = moment().subtract(days, 'days').valueOf()
      break
    case 'none':
    default:
      start = ''
      break
  }
  return { start, end, range }
}

export function getCollectionPath(collection) {
  if (!isLoopableObject(AppModules)) {
    AppModules = require('Constants/AppModules')
  }
  let Modules = JSON.parse(JSON.stringify(AppModules))

  let Module = Modules[collection]
  if (!Module) {
    Modules = require('Constants/AppModules')
    Module = Modules[collection]
  }
  let route = Module.route || collection
  return route
}

var prepoNomes = ['do', 'da', 'dos', 'das', 'de']
var currentLanguage = 'pt'

if (!window.textosLang) var textosLang = { pt: {} }
if (!textosLang.pt) textosLang['pt'] = {}

textosLang.pt['confirm_cep_invalido'] = 'Não foi possível verificar o CEP. Deseja inserir assim mesmo?'
textosLang.pt['alert_cep_invalido'] =
  'Desculpe, não foi possível verificar o CEP. Confira o número inserido ou, no caso de dúvidas, comunique-se conosco.'
textosLang.pt['error_nasc_formato'] = 'Por favor, informe uma data de nascimento no formato DD/MM/AAAA'
textosLang.pt['error_nasc_dia'] = 'Por favor, informe um dia válido para a data de nascimento'
textosLang.pt['error_nasc_mes'] = 'Por favor, informe um mês válido para a data de nascimento'
textosLang.pt['error_nasc_ano'] = 'Por favor, informe o ano com 4 dígitos'
textosLang.pt['error_nasc_data'] = 'Por favor, informe uma data de nascimento válida'
textosLang.pt['error_nasc_idade'] = 'Você deve ser maior a [n] anos para fazer o registro no site'
textosLang.pt['error_ajax'] = 'Parece que há um problema de conexão. Tente novamente mais tarde ou comunique-se com o administrador'
textosLang.pt['error_ajax_unsuportted'] = 'Parece que o aplicativo não aceita a execução de ajax requests'

var hasJquery = typeof $ === 'function'

/**
 * Get Date
 */
export function currentDate(format) {
  let formatDate = format ? format : MOMENT_ISO
  return moment().format(formatDate)
}

// JSON
// --------------------------
export const jsonClone = obj => {
  if (!obj || typeof obj !== 'object') return obj
  try {
    var clone = JSON.parse(JSON.stringify(obj))
    Object.keys(obj).forEach((key, i, keys) => {
      if (obj[key] && typeof obj[key] === 'object') {
        clone[key] = window.jsonClone(obj[key])
      } else if (typeof obj[key] === 'function') {
        clone[key] = obj[key]
      }
    })
    return clone
  } catch (err) {
    return obj
  }
}

JSON.clone = jsonClone

export const jsonMerge = objs => {
  const newObj = {}
  const getNodes = (nodeObjs, nodeKey) => {
    const targetNodes = {}
    Object.keys(nodeObjs).forEach(k => {
      if (isLoopableObject(nodeObjs[k]) && nodeKey in nodeObjs[k]) {
        targetNodes[k] = nodeObjs[k][nodeKey]
      }
    })
    return targetNodes
  }
  Object.keys(objs).forEach(k => {
    const thisObj = objs[k]
    // target object
    if (isLoopableObject(thisObj)) {
      Object.keys(thisObj).forEach(key => {
        let thisNode = thisObj[key]
        // level 1 of target object
        if (isLoopableObject(thisNode)) {
          const targetNodes = getNodes(objs, key)
          newObj[key] = jsonMerge(targetNodes)
        } else {
          newObj[key] = thisNode
        }
      })
    }
  })
  return newObj
}

JSON.merge = jsonMerge

// ajax wrapper
// --------------------------
// grabHereToCopyPaste
export const qiAjax = (params, always) => {
  if (!params.url) params.url = WP_AJAX_URL
  if (!params.type) params.type = 'POST'

  let data = {
    ...(params.data || {}),
    origin: params.data.origin || QIPLUS_APP_ORIGIN,
    enviroinment: params.data.enviroinment || APP_ENVIROINMENT,
  }

  if (!data.action && params.url === WP_AJAX_URL) alert('missing action param')

  if (APP_ENVIROINMENT === 'development') {
    console.log('qiAjax > request > params', params)
  }

  $.ajax({ ...params, data }).always(function (response) {
    if (typeof always === 'function') {
      always(response)
    }
  })
}

// KEYWORDS
// --------------------------
// grabHereToCopyPaste
export const toSearchKeyword = (name, minLen, maxLen, sep) => {
  const k = createKeywords(name, minLen || 1, maxLen, sep)
  return k.pop()
}

// grabHereToCopyPaste
export const createKeywords = (name, minLen, maxLen, sep) => {
  minLen = minLen || 2
  maxLen = maxLen || 100
  const keywords = []
  let curName = ''
  name = name && name.toString ? name.toString() : ''
  name.split(sep || '').forEach((f, i) => {
    const fragment = (f && f.toLowerCase()) || ''
    if (fragment) {
      curName += fragment
      i >= minLen - 1 && keywords.push(removeSpaces(curName))
    }
  })
  return keywords.filter(k => k.length < maxLen)
}

// grabHereToCopyPaste
export const generateKeywords = (data, minLen, maxLen, substrKeys, substrLimit, minSubstrLen, sep) => {
  let keywords = []
  substrLimit = Math.min(50, substrLimit || 50)
  substrKeys = Array.isArray(substrKeys) ? substrKeys : []
  Object.keys(data).forEach((key, i) => {
    let val = data[key]
    if (['number', 'string'].includes(typeof val) && val.toString) {
      let k = createKeywords(val, minLen, maxLen, sep)
      if (val.toString().length < substrLimit && substrKeys.includes(key)) {
        k = generateSubstrKeywords(k, minSubstrLen)
      }
      keywords = keywords.concat(k)
    }
  })
  return [...new Set(keywords)]
}

// grabHereToCopyPaste
export const createLongTextKeywords = (text, minLen, maxLen) => {
  minLen = minLen || 2
  maxLen = maxLen || 100
  text = text.toString ? text.toString() : ''
  let keywords = []
  text.split(' ').forEach((w, i) => {
    const word = (w && w.length >= minLen && w.toLowerCase()) || ''
    if (word) {
      keywords = [
        ...new Set([...keywords, ...createKeywords(word, minLen, maxLen)]),
      ]
    }
  })
  return [...new Set(keywords)].filter(k => k.length < maxLen)
}

// grabHereToCopyPaste
export const generateLongTextKeywords = (data, minLen, sep) => {
  let keywords = []
  Object.keys(data).forEach((key, i) => {
    let val = data[key]
    if (['number', 'string'].includes(typeof val) && val.toString) {
      let k = createLongTextKeywords(val, minLen, sep)
      keywords = keywords.concat(k)
    }
  })
  return [...new Set(keywords)]
}

// grabHereToCopyPaste
export const generateSubstrKeywords = (keywords, minSubstrLen) => {
  minSubstrLen = Math.max(2, minSubstrLen || 2)
  let newkeywords = window.jsonClone(keywords)
  keywords.forEach(k => {
    if (k.length > minSubstrLen) {
      let p = 0
      do {
        p++
        const substr = k.substr(p, k.length)
        newkeywords.push(substr)
      } while (p < k.length - minSubstrLen)
    }
  })
  return [...new Set(newkeywords)]
}

// grabHereToCopyPaste
export const generateKeywordsDocs = keywordsSource => {
  const { KEYWORDS_FIELDS_MAP } = COLLECTIONS

  let post = keywordsSource
  let docId = post.ID
  let { collection, owner, accountId } = post

  if (!KEYWORDS_FIELDS_MAP[collection]) {
    console.log('No KEYWORDS_FIELDS_MAP', {
      collection,
      docId,
      owner,
      accountId,
    })
    return []
  }

  const { allKeywordFields, inheritedFields, joinFields, limitedFields } = KEYWORDS_FIELDS_MAP[collection]

  const keywordDocs = []

  const inheritedData = {
    // docRef,
    docId,
    owner,
    accountId,
  }

  joinFields.forEach(field => {
    if (typeof keywordsSource[field] !== 'undefined') inheritedData[field] = keywordsSource[field]
  })

  inheritedFields.forEach(field => {
    if (typeof keywordsSource[field] !== 'undefined') inheritedData[field] = keywordsSource[field]
  })

  const relationalFields = CONSTANTS.RELATIONAL_FIELDS.filter(k => {
    return k in keywordsSource && ['number', 'string'].includes(typeof keywordsSource[k]) && Boolean(keywordsSource[k].toString())
  })

  relationalFields.forEach(field => {
    let value = keywordsSource[field]
    let keywords = [value]
    keywordDocs.push({
      ...inheritedData,
      field,
      value,
      [field]: value,
      [CONSTANTS.KEYWORDS_FIELD]: keywords,
      index: field,
    })
  })

  limitedFields
    .filter(
      k =>
        (k in keywordsSource && Array.isArray(keywordsSource[k])) ||
        (['number', 'string'].includes(typeof keywordsSource[k]) && Boolean(keywordsSource[k].toString()))
    )
    .forEach(field => {
      let value = keywordsSource[field]
      let keywords = Array.isArray(value) ? value : [value]
      keywordDocs.push({
        ...inheritedData,
        field,
        value,
        [field]: value,
        [CONSTANTS.KEYWORDS_FIELD]: keywords,
        index: field,
      })
    })

  allKeywordFields
    .filter(k => {
      return k in keywordsSource && ['number', 'string'].includes(typeof keywordsSource[k]) && Boolean(keywordsSource[k].toString())
    })
    .forEach(field => {
      let value = keywordsSource[field]
      let keywords =
        field === 'description' || value.toString().split(' ').length > 10
          ? generateLongTextKeywords({ [field]: value })
          : generateKeywords({ [field]: value }, 2, 100, [field])
      if (utf8BytesSize(keywords) > CONSTANTS.FIRESTORE_SAFE_DOCUMENT_FIELD_SIZE) {
        keywords = []
      }

      switch (field) {
        case 'cpf':
        case 'cnpj':
        case 'mobile':
        case 'phone':
        case 'postalCode':
          keywords = [
            ...new Set([
              ...keywords,
              ...keywords.map(k => `${convertTonumOnly(k)}`),
            ]),
          ]
          break
        default:
          break
      }

      keywordDocs.push({
        ...inheritedData,
        field,
        value,
        [field]: value,
        [CONSTANTS.KEYWORDS_FIELD]: keywords,
        index: field,
      })
    })

  if (isLoopableObject(post.customFields)) {
    Object.keys(post.customFields)
      .filter(c => {
        return (
          Array.isArray(post.customFields[c]) ||
          (['number', 'string'].includes(typeof post.customFields[c]) && Boolean(post.customFields[c].toString()))
        )
      })
      .forEach((c, i) => {
        let field = c
        let value = post.customFields[c]

        let keywords = Array.isArray(value)
          ? value
          : value && value.toString && value.toString().split(' ').length > 10
            ? generateLongTextKeywords({ [field]: value })
            : generateKeywords({ [field]: value }, 2, 100, [field])

        if (utf8BytesSize(keywords) > CONSTANTS.FIRESTORE_SAFE_DOCUMENT_FIELD_SIZE) {
          keywords = []
        }

        keywordDocs.push({
          ...inheritedData,
          group: CONSTANTS.CUSTOM_FIELDS_GROUP,
          field,
          value,
          [field]: value,
          [CONSTANTS.KEYWORDS_FIELD]: keywords,
          index: `${c.replace(`${CONSTANTS.CUSTOM_FIELDS_PATH}:`, `${CONSTANTS.CUSTOM_FIELDS_PATH}_`)}`,
        })
      })
  }

  if (Array.isArray(post.tags) && post.tags.length) {
    post.tags
      .filter(c => Boolean(c))
      .forEach((t, i) => {
        let field = 'tags'
        let value = t
        let keywords = [value]
        keywordDocs.push({
          ...inheritedData,
          field,
          value,
          [field]: value,
          [CONSTANTS.KEYWORDS_FIELD]: keywords,
          index: `${field}_${i}`,
        })
      })
  }

  if (Array.isArray(post.comments) && post.comments.length) {
    post.comments
      .filter(c => Boolean(c.content))
      .forEach((c, i) => {
        let field = 'comments'
        let value = c.content
        let keywords = generateLongTextKeywords({ [field]: value })
        if (utf8BytesSize(keywords) > CONSTANTS.FIRESTORE_SAFE_DOCUMENT_FIELD_SIZE) {
          keywords = []
        }
        keywordDocs.push({
          ...c,
          ...inheritedData,
          field,
          value,
          [field]: value,
          [CONSTANTS.KEYWORDS_FIELD]: keywords,
          index: `${field}_${i}`,
        })
      })
  }

  return keywordDocs
}

export const combineLogsStats = ({ response, collection, dateFormat, collectionStats, contactsData }) => {
  collectionStats = { ...(collectionStats || {}) }
  contactsData = { ...(contactsData || {}) }

  const statsModel = {
    collection: '',
    contactIds: [],
    labels: [],
    data: [],
    counters: {},
    accumulators: {},
    combined: {},
    related: {},
    values: {},
    days: {},
    weeks: {},
    months: {},
    daily: 0,
    weekly: 0,
    monthly: 0,
    total: 0,
  }

  let relationalModel = {
    axis: {},
    counters: {},
    accumulators: {},
    combined: {},
  }

  Object.keys(response).forEach(postId => {
    const { axisData, counters, accumulators, combined } = response[postId]

    Object.keys((axisData || {}).aData || {}).forEach((trigger, i) => {
      if (!collectionStats[trigger]) {
        collectionStats[trigger] = {
          ...window.jsonClone(statsModel),
          collection,
        }
      }

      let triggerStats = collectionStats[trigger]
      let triggerValues = axisData.aData[trigger][dateFormat] || {}
      let triggerContacts = axisData.cData[trigger][dateFormat] || {}
      let triggerRelations = axisData.rData[trigger] || {}

      Object.keys(triggerRelations).forEach((field, i) => {
        const fieldData = triggerRelations[field][dateFormat]
        Object.keys(fieldData).forEach((dateStamp, i) => {
          let rIds = fieldData[dateStamp]

          rIds.forEach(rId => {
            let relatedTrigger = triggerStats.related || {}
            let relatedField = relatedTrigger[field] || {}
            let relatedData = relatedField[rId] || {}
            let relatedFormat = relatedData[dateFormat] || {}
            let relatedCount = parseFloat(relatedFormat[dateStamp] || 0)

            triggerStats.related = {
              ...relatedTrigger,
              [field]: {
                ...relatedField,
                [rId]: {
                  ...relatedData,
                  [dateFormat]: {
                    ...relatedFormat,
                    [dateStamp]: relatedCount + 1,
                  },
                },
              },
            }
          })
        })
      })

      Object.keys(triggerContacts).forEach((dateStamp, i) => {
        let contactIds = triggerContacts[dateStamp]
        contactIds.forEach(cId => {
          let contactData = contactsData[cId] || {}
          let cCollection = contactData[collection] || {
            [trigger]: window.jsonClone(relationalModel),
          }
          let cTriggers = cCollection[trigger] || {}
          let cAxis = cTriggers.axis || {}
          let cAccum = cTriggers.accumulators || {}

          if (accumulators[trigger]) {
            Object.keys(accumulators[trigger]).forEach((key, i) => {
              cAccum[key] = parseFloat(cAccum[key] || 0) + accumulators[trigger][key]
            })
          }

          contactsData[cId] = {
            ...contactData,
            [collection]: {
              ...cCollection,
              [trigger]: {
                ...cTriggers,
                accumulators: cAccum,
                axis: {
                  ...cAxis,
                  [dateStamp]: parseFloat(cAxis[dateStamp] || 0) + 1,
                },
              },
            },
          }
        })
        triggerStats.contactIds = [
          ...new Set([...triggerStats.contactIds, ...contactIds]),
        ]
      })

      if (isset(combined, [trigger, CONSTANTS.CONTACT_ID_FIELD])) {
        let contactAccumulators = combined[trigger][CONSTANTS.CONTACT_ID_FIELD]
        Object.keys(contactAccumulators).forEach(cId => {
          Object.keys(contactAccumulators[cId]).forEach((key, i) => {
            let contactData = contactsData[cId] || {}
            let cCollection = contactData[collection] || {
              [trigger]: window.jsonClone(relationalModel),
            }
            let cTriggers = cCollection[trigger] || {}
            let cAccumtrs = cTriggers.combined || {}

            contactsData[cId] = {
              ...contactData,
              [collection]: {
                ...cCollection,
                [trigger]: {
                  ...cTriggers,
                  combined: {
                    ...cAccumtrs,
                    [key]: parseFloat(cAccumtrs[key] || 0) + parseFloat(contactAccumulators[cId][key] || 0),
                  },
                },
              },
            }
          })
        })
      }

      if (isset(counters, [trigger, CONSTANTS.CONTACT_ID_FIELD])) {
        let contactCounters = counters[trigger][CONSTANTS.CONTACT_ID_FIELD]

        contactCounters.forEach(d => {
          let { key, value, count } = d
          let cId = value,
            field = key

          let contactData = contactsData[cId] || {}
          let cCollection = contactData[collection] || {
            [trigger]: window.jsonClone(relationalModel),
          }
          let cTriggers = cCollection[trigger] || {}
          let cCounters = cTriggers.counters || {}

          let foundIndex = (cCounters[field] || []).findIndex(t => t.key === key && t.value === value)
          if (foundIndex >= 0) {
            cCounters[field][foundIndex] = {
              ...cCounters[field][foundIndex],
              count: ((cCounters[field][foundIndex] || {}).count || 0) + count,
            }
          } else {
            cCounters[field] = [...(cCounters[field] || []), d]
          }

          contactsData[cId] = {
            ...contactData,
            [collection]: {
              ...cCollection,
              [trigger]: {
                ...cTriggers,
                counters: {
                  ...cCounters,
                },
              },
            },
          }
        })
      }

      Object.keys(triggerValues).forEach((dateStamp, i) => {
        let value = Number(triggerValues[dateStamp])
        triggerStats.values[dateStamp] = (triggerStats.values[dateStamp] || 0) + value

        let day = moment(dateStamp).format(CONSTANTS.MOMENT_ISO_DAY)
        triggerStats.days[day] = (triggerStats.days[day] || 0) + value

        let week = moment(dateStamp).format(CONSTANTS.MOMENT_ISO_WEEK)
        triggerStats.weeks[week] = (triggerStats.weeks[week] || 0) + value

        let month = moment(dateStamp).format(CONSTANTS.MOMENT_ISO_MONTH)
        triggerStats.months[month] = (triggerStats.months[month] || 0) + value
      })

      let daysValues = Object.values(triggerStats.days)
      triggerStats.total = daysValues.reduce((all, v) => all + v, 0)

      // daily
      let daysKeys = Object.keys(triggerStats.days)
      daysKeys.sort((a, b) => moment(a).valueOf() - moment(b).valueOf())

      let firstDay = daysKeys[0]
      let lastDay = daysKeys[daysKeys.length - 1]

      // daily
      let diffDays = moment(lastDay).diff(moment(firstDay), 'days') || 1
      triggerStats.daily = Math.ceil(triggerStats.total / diffDays)

      // weekly
      let diffWeeks = Math.max(1, diffDays / 7)
      triggerStats.weekly = Math.ceil(triggerStats.total / diffWeeks)

      // monthly
      let diffMonths = Math.max(1, diffDays / 30)
      triggerStats.monthly = Math.ceil(triggerStats.total / diffMonths)

      counters[trigger] &&
        Object.keys(counters[trigger]).forEach((field, i) => {
          if (Array.isArray(counters[trigger][field])) {
            counters[trigger][field].forEach(d => {
              let { key, value, count } = d
              let foundIndex = (triggerStats.counters[field] || []).findIndex(t => t.key === key && t.value === value)
              if (foundIndex >= 0) {
                triggerStats.counters[field][foundIndex] = {
                  ...triggerStats.counters[field][foundIndex],
                  count: ((triggerStats.counters[field][foundIndex] || {}).count || 0) + count,
                }
              } else {
                triggerStats.counters[field] = [
                  ...(triggerStats.counters[field] || []),
                  d,
                ]
              }
            })

            isset(combined, [trigger, field]) &&
              Object.keys(combined[trigger][field]).forEach((rId, i) => {
                accumulators[trigger] &&
                  Object.keys(accumulators[trigger]).forEach((key, i) => {
                    const value = getDeepValue(combined, [trigger, field, rId, key], 0)
                    const sum = getDeepValue(triggerStats.combined, [field, rId, key], 0)
                    const combinedStats = triggerStats.combined || {}
                    const fieldStats = combinedStats[field] || {}

                    triggerStats.combined = {
                      ...combinedStats,
                      [field]: {
                        ...fieldStats,
                        [rId]: {
                          ...(fieldStats[rId] || {}),
                          [key]: parseFloat(value) + parseFloat(sum),
                        },
                      },
                    }
                  })
              })
          }
        })

      accumulators[trigger] &&
        Object.keys(accumulators[trigger]).forEach((k, i) => {
          triggerStats.accumulators[k] = (triggerStats.accumulators[k] || 0) + accumulators[trigger][k]
        })

      if (collection === 'YYYY-MM-DD') {
        console.log('Trigger Stats > ', collection, {
          triggerStats,
          collectionStats,
        })
      }

      collectionStats[trigger] = {
        ...collectionStats[trigger],
        ...triggerStats,
      }
    })
  })

  return {
    collectionStats,
    contactsData,
  }
}

export const compileLogsStats = (logs, collection, dateFormats, counters, accumulators) => {
  counters = counters || []
  accumulators = accumulators || []
  dateFormats = Array.isArray(dateFormats) ? dateFormats : [dateFormats]

  let dataModel = {
    axis: {},
    aData: {},
    cData: {},
    rData: {},
    counters: {},
    accumulators: {},
    combined: {},
    contactIds: [],
  }

  let collectionData = JSON.parse(JSON.stringify(dataModel))
  let collectionsData = {}

  logs.forEach((log, i) => {
    const logCollection = log.collection

    let { trigger, contactId, createdAt, date, data, context } = log

    let logData = { ...(data || {}) }

    if (contactId && !logData.contactId) {
      logData.contactId = contactId
    }

    if (isLoopableObject(context)) {
      Object.keys(context)
        .filter(field => isDefined(context[field], false, false, true) && !logData[field] && !['collection', 'id'].includes(field))
        .forEach(field => {
          logData[field] = context[field]
        })
    }

    let targetData = collectionData

    if (logCollection !== collection) {
      collectionsData[logCollection] = collectionsData[logCollection] || JSON.parse(JSON.stringify(dataModel))
      targetData = collectionsData[logCollection]
    }

    targetData.counters[trigger] = targetData.counters[trigger] || {}
    targetData.accumulators[trigger] = targetData.accumulators[trigger] || {}
    targetData.combined[trigger] = targetData.combined[trigger] || {}

    counters.forEach(field => {
      targetData.counters[trigger][field] = targetData.counters[trigger][field] || []
      if (field in logData && logData[field]) {
        let value = logData[field]
        let counterIndex = targetData.counters[trigger][field].findIndex(c => c.value === value)
        if (counterIndex === -1) {
          targetData.counters[trigger][field].push({
            key: field,
            value,
            count: 1,
          })
        } else {
          targetData.counters[trigger][field][counterIndex].count++
        }
      }
    })

    accumulators.forEach(key => {
      targetData.accumulators[trigger][key] = targetData.accumulators[trigger][key] || 0
      if (key in logData && isNumeric(logData[key])) {
        let value = Number(logData[key])
        targetData.accumulators[trigger][key] += value
        counters.forEach(field => {
          targetData.combined[trigger][field] = targetData.combined[trigger][field] || {}
          if (logData[field]) {
            let rId = logData[field]
            targetData.combined[trigger][field][rId] = targetData.combined[trigger][field][rId] || {}
            targetData.combined[trigger][field][rId][key] = targetData.combined[trigger][field][rId][key] || 0
            targetData.combined[trigger][field][rId][key] += value
          }
        })
      }
    })

    contactId &&
      (targetData.contactIds = [
        ...new Set([...targetData.contactIds, contactId]),
      ])

    // axisData
    dateFormats.forEach(f => {
      let ddd = moment(date).format(f)

      // axis
      !targetData.axis[f] && (targetData.axis[f] = [])
      targetData.axis[f] = [...new Set([...targetData.axis[f], ddd])]

      // aData
      !targetData.aData[trigger] && (targetData.aData[trigger] = {})
      !targetData.aData[trigger][f] && (targetData.aData[trigger][f] = {})
      !targetData.aData[trigger][f][ddd] && (targetData.aData[trigger][f][ddd] = 0)
      targetData.aData[trigger][f][ddd]++

      // cData
      !targetData.cData[trigger] && (targetData.cData[trigger] = {})
      !targetData.cData[trigger][f] && (targetData.cData[trigger][f] = {})
      !targetData.cData[trigger][f][ddd] && (targetData.cData[trigger][f][ddd] = [])
      contactId && targetData.cData[trigger][f][ddd].push(contactId)

      counters.forEach(field => {
        !targetData.rData[trigger] && (targetData.rData[trigger] = {})
        !targetData.rData[trigger][field] && (targetData.rData[trigger][field] = {})
        !targetData.rData[trigger][field][f] && (targetData.rData[trigger][field][f] = {})
        !targetData.rData[trigger][field][f][ddd] && (targetData.rData[trigger][field][f][ddd] = [])
        if (logData[field]) {
          let rId = logData[field]
          targetData.rData[trigger][field][f][ddd].push(rId)
        }
      })
    })
  })

  Object.keys(collectionsData).forEach((col, i) => {
    let { axis, aData, cData, rData } = collectionsData[col]
    collectionsData[col].axisData = { axis, aData, cData, rData }
  })

  let size = logs.length
  let { axis, aData, cData, rData } = collectionData
  let stats = {
    ...collectionData,
    size,
    axisData: { axis, aData, cData, rData },
    collectionsData,
  }

  return stats
}

export const buildDatasetsFromStats = ({
  stats,
  labelFormat,
  dateFormat,
  adjustDateFormat,
  areaChartTriggers,
  areaChartColors,
  areaChartLabels,
  axisDays,
  datasets,
  filters,
  completeDateRange,
}) => {
  filters = { ...(filters || {}) }
  axisDays = [...(axisDays || [])]
  datasets = [...(datasets || [])]

  Object.keys(stats).forEach(collection => {
    Object.keys(stats[collection]).forEach((trigger, i) => {
      const triggerStats = stats[collection][trigger]
      const triggerColor = (areaChartColors[collection] || {})[trigger] || areaChartColors[collection]
      const triggerLabel = (areaChartLabels[collection] || {})[trigger] || areaChartLabels[collection]

      let shouldEvaluateTrigger = trigger === areaChartTriggers[collection]
      if (Array.isArray(areaChartTriggers[collection])) {
        shouldEvaluateTrigger = areaChartTriggers[collection].includes(trigger)
      }

      if (shouldEvaluateTrigger && triggerStats.values) {
        let collectionDays = []

        if (completeDateRange !== false && filters.start && filters.end) {
          let diffDays = moment(filters.end).diff(moment(filters.start), 'days') || 1
          if (diffDays)
            for (let d = 0; d <= diffDays; d++) {
              collectionDays.push(moment(filters.start).add(d, 'days').format(dateFormat))
            }
          else {
            collectionDays = [
              moment(filters.start).format(dateFormat),
              moment(filters.start).add(1, 'days').format(dateFormat),
            ]
          }
        }

        collectionDays = [
          ...new Set([...collectionDays, ...Object.keys(triggerStats.values)]),
        ]
          .filter(dateStamp => {
            if (filters.start && moment(dateStamp).valueOf() < filters.start) return false
            if (filters.end && moment(dateStamp).valueOf() > filters.end) return false
            return true
          })
          .sort((a, b) => moment(a).valueOf() - moment(b).valueOf())

        let collectionData = collectionDays.map(dateStamp => triggerStats.values[dateStamp] || 0)

        let dataset = {
          label: triggerLabel,
          borderColor: triggerColor,
          pointBackgroundColor: triggerColor,
          data: collectionData,
          days: collectionDays,
          collectionDays,
          collection,
          trigger,
        }

        let datasetIndex = datasets.findIndex(d => d.label === triggerLabel)

        if (datasetIndex === -1) {
          datasets.push(dataset)
        } else {
          datasets[datasetIndex] = dataset
        }

        axisDays = [...new Set([...axisDays, ...collectionDays])].sort((a, b) => moment(a).valueOf() - moment(b).valueOf())

        // console.log(collection+' 1',{ collection, dataset, datasets, datasetIndex, axisDays });
      }
    })

    // console.log(collection+' 2',{  stats, labelFormat, areaChartTriggers, areaChartColors, areaChartLabels, axisDays, datasets, filters  });
  })

  const adjustedFormat = adjustDateFormat !== false && axisDays.length > 10 ? CONSTANTS.MOMENT_DAY_MONTH : labelFormat

  return {
    axisDays,
    labels: axisDays.map(dateStamp => moment(dateStamp).format(adjustedFormat)),
    datasets: datasets.map(set => ({
      ...set,
      days: axisDays,
      data: axisDays.map(dateStamp => (set.days.includes(dateStamp) ? set.data[set.days.findIndex(d => d === dateStamp)] : 0)),
    })),
  }
}

export const isCurrentMenu = (menu, pathname) => {
  pathname = pathname || window.location.pathname.replace('/app', '')
  const inCollection = getBodyAttributeValue('data-collection')
  // let isCurrent = isCurrentMenu(menu)
  // let subMenu = (menu.child_routes||[]).find(isCurrentMenu);
  // if (isCurrent && pathname === menu.path ) {
  //    console.log('\nisCurrent ---------------');
  //    console.log('isCurrent > menu',menu);
  // }else
  // if ( isCurrent && subMenu ) {
  //    console.log('\nisCurrent ---------------');
  //    console.log('isCurrent > subMenu', subMenu);
  //    console.log('isCurrent > subMenu > menu', menu);
  //    console.log('isCurrent > subMenu > inCollection', inCollection);
  //    console.log('isCurrent > subMenu > menu.collection', menu.collection);
  //    console.log('isCurrent > subMenu > subMenu.collection', subMenu.collection);
  // }else
  // if ( isCurrent ) {
  //    console.log('\nisCurrent ---------------');
  //    console.log('isCurrent > last > menu', menu);
  // }
  return (
    pathname === menu.path ||
    (menu.child_routes &&
      !!menu.child_routes.find(subM => {
        return subM.child_routes
          ? !!isCurrentMenu(subM)
          : pathname === subM.path ||
          (pathname.indexOf(subM.path) !== -1 &&
            ((!inCollection && !menu.collection && !subM.collection) || [menu.collection, subM.collection].includes(inCollection)))
      }))
  )
}

export function getScrollParent(node) {
  if (node == null) {
    return null
  }
  if (node.scrollHeight > node.clientHeight) {
    return node
  } else {
    return getScrollParent(node.parentNode)
  }
}

export function isXLScreen(returnParam, width) {
  if (!hasJquery) return false

  width = isDefined(width) ? width : 1240
  var wWidth = $(window).width()

  var resIndex = 6
  var resSufix = 'xx'

  return returnParam === 'sufix' ? resSufix : returnParam === 'index' ? resIndex : wWidth > width
}

export function isPC(returnParam, width) {
  if (!hasJquery) return false

  width = isDefined(width) ? width : 1240
  var wWidth = $(window).width()

  var resIndex = 5
  var resSufix = 'xl'

  return returnParam === 'sufix' ? resSufix : returnParam === 'index' ? resIndex : wWidth <= width
}

export function isLGScreen(returnParam, width) {
  if (!hasJquery) return false

  width = isDefined(width) ? width : 1240
  var wWidth = $(window).width()

  var resIndex = 4
  var resSufix = 'lg'

  return returnParam === 'sufix' ? resSufix : returnParam === 'index' ? resIndex : wWidth <= width
}

export function isTablet(returnParam, width) {
  if (!hasJquery) return false

  width = isDefined(width) ? width : 768
  var wWidth = $(window).width()

  var resIndex = 3
  var resSufix = 'md'

  return returnParam === 'sufix' ? resSufix : returnParam === 'index' ? resIndex : wWidth <= width
}

export function isMobile(returnParam, width) {
  if (!hasJquery) return false

  width = isDefined(width) ? width : 480
  var wWidth = $(window).width()

  var resIndex = 2
  var resSufix = 'sm'

  return returnParam === 'sufix' ? resSufix : returnParam === 'index' ? resIndex : wWidth <= width
}

export function isXSScreen(returnParam, width) {
  if (!hasJquery) return false

  width = isDefined(width) ? width : 320
  var wWidth = $(window).width()

  var resIndex = 1
  var resSufix = 'xs'

  return returnParam === 'sufix' ? resSufix : returnParam === 'index' ? resIndex : wWidth <= width
}

export function rightClick(e) {
  return e.which == 3 || e.button == 2
}

export function leftClick(e) {
  return e.which == 1 || e.button == 1
}

export function hasFocus(filter) {
  if (!hasJquery) return false
  return $(':focus').hasFocus(filter)
}

export function getFocused(filter) {
  return document.querySelectorAll(':focus')[0]
}

export function chunkArray(arr, chunkSize) {
  var newArray = []
  for (var index = 0; index < arr.length; index += chunkSize) {
    let chunk = arr.slice(index, index + chunkSize)
    newArray.push(chunk)
  }
  return newArray
}

export async function chunkPromises(promises, limit, progressFn, index, results) {
  index = index || 0
  results = results || []
  const chunks = chunkArray(promises, limit)
  try {
    const r = await Promise.all(chunks[index])
    results = results.concat(r)
    progressFn && progressFn(results, index)
    return index === chunks.length - 1 ? results : chunkPromises(promises, limit, progressFn, index + 1, results)
  } catch (error) {
    console.error('Error in promises:', error)
    return error
  }
}

export function arrayHasVal(array, value, exactMatch) {
  if (!isDefined(value, true, false) || !Array.isArray(array)) {
    return false
  }
  exactMatch = exactMatch !== false
  var returnVal = false
  array.forEach(child => {
    if (typeof child === 'object' && Object.keys(child).length) {
      Object.keys(child).forEach(key => {
        if ((exactMatch && child[key] === value) || (!exactMatch && hasStr(child[key], value))) {
          returnVal = true
        }
      })
    } else if ((exactMatch && child === value) || (!exactMatch && hasStr(child, value))) {
      returnVal = true
    }
  })
  return returnVal
}

export function objArrayHasVal(array, value, exactMatch) {
  if (!isDefined(value, true, false) || !Array.isArray(array)) {
    return false
  }

  exactMatch = exactMatch !== false

  for (var i = 0; i < array.length; i++) {
    if (typeof array[i] === 'object' && Object.keys(array[i]).length) {
      var returnVal = false
      Object.keys(array[i]).forEach(function (key) {
        if ((exactMatch && array[i][key] === value) || (!exactMatch && hasStr(array[i][key], value))) {
          returnVal = true
        }
      })
      if (returnVal) return true
    }
  }

  return false
}

// grabHereToCopyPaste
export const isCyclic = obj => {
  var seenObjects = []
  function detect(obj, level) {
    level = level || 0
    var key
    if (isLoopable(obj)) {
      let keys = Object.keys(obj)
      let seenObjectIndex = seenObjects.indexOf(obj)
      if (seenObjectIndex > -1 && level > 1) {
        for (let k = 0; k < keys.length; k++) {
          key = keys[k]
          if (isLoopable(obj[key])) {
            console.log('cycle in index ' + seenObjectIndex, {
              seenObjects,
              obj,
            })
            return true
          }
        }
      }
      seenObjects.push(obj)
      for (let k = 0; k < keys.length; k++) {
        key = keys[k]
        if (detect(obj[key], level + 1)) {
          console.log('cycle at ' + key, obj)
          return true
        }
      }
    }
    return false
  }
  return detect(obj)
}

// grabHereToCopyPaste
export const replaceUndefined = (obj, replacement) => {
  if (isLoopable(obj)) {
    Object.keys(obj).forEach(key => {
      if (obj[key] === undefined || typeof obj[key] === 'undefined') {
        // console.log('replaceUndefined >>>> FOUND UNDEFINED -------------------------');
        obj[key] = replacement || null
      } else if (isLoopable(obj[key]) && !isCyclic(obj[key])) {
        obj[key] = replaceUndefined(obj[key], replacement)
      }
    })
  }
  return obj
}

// grabHereToCopyPaste
export const replaceKeysRecursively = (obj, replaceKeys) => {
  if (Array.isArray(obj)) {
    obj.forEach((a, i) => {
      if (isLoopable(a) && !isCyclic(a)) replaceKeysRecursively(a)
    })
  } else if (obj && typeof obj === 'object' && Object.keys(obj) && Object.keys(obj).length) {
    Object.keys(replaceKeys).forEach((key, i) => {
      // ------------------------------------------------
      // MAGIC HAPPENS HERE
      // ------------------------------------------------
      if (key in obj) obj[replaceKeys[key]] = obj[key]
      if (key in obj) delete obj[key]
    })
    Object.keys(obj).forEach(key => {
      if (typeof obj[key] === 'object' && Object.keys(obj[key]).length) {
        if (!isCyclic(obj[key])) replaceKeysRecursively(obj[key])
      } else if (Array.isArray(obj[key])) {
        if (!isCyclic(obj[key])) replaceKeysRecursively(obj[key])
      }
    })
  }
}

// grabHereToCopyPaste
export const deleteRecursively = (obj, deleteKeys) => {
  if (Array.isArray(obj)) {
    obj.forEach((a, i) => {
      if (!isCyclic(a) && isLoopable(a)) {
        deleteRecursively(a, deleteKeys)
      }
    })
  } else if (obj && typeof obj === 'object' && Object.keys(obj) && Object.keys(obj).length) {
    deleteKeys.forEach((key, i) => {
      // ------------------------------------------------
      // MAGIC HAPPENS HERE
      // ------------------------------------------------
      if (key in obj) delete obj[key]
      // if (key in obj) console.log('deleteRecursively > key', key);
    })
    Object.keys(obj).length &&
      Object.keys(obj).forEach(key => {
        if (typeof obj[key] === 'object' && Object.keys(obj[key]).length) {
          if (!isCyclic(obj[key])) deleteRecursively(obj[key], deleteKeys)
        } else if (Array.isArray(obj[key])) {
          if (!isCyclic(obj[key])) deleteRecursively(obj[key], deleteKeys)
        }
      })
  }
}

export const escapeRegExp = string => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // $& means the whole matched string
}

export const replaceAll = function (searchStr, replaceStr, contentStr) {
  if (isDefined(searchStr, true, true) && isDefined(replaceStr, true, true)) {
    let newStr = contentStr
    let searchArr = Array.isArray(searchStr) ? searchStr : [searchStr]
    let replaceArr = Array.isArray(replaceStr) ? replaceStr : [replaceStr]
    if (replaceArr.length < searchArr.length) {
      for (let i = 0; i < searchArr.length; i++) {
        replaceArr[i] = replaceArr.length === 1 ? replaceArr[0] : i > replaceArr.length - 1 ? '' : replaceArr[i]
      }
    }
    for (let i = 0; i < searchArr.length; i++) {
      let sStr = searchArr[i]
      let rStr = replaceArr[i]
      if (sStr === '.') {
        newStr = newStr.replace(/\./g, rStr)
      } else {
        let regexStr = escapeRegExp(sStr)
        newStr = newStr.replace(new RegExp(regexStr, 'g'), rStr)
      }
    }
    return newStr
  } else {
    return contentStr.toString()
  }
}

if (!String.prototype.replaceAll) {
  // eslint-disable-next-line no-extend-native
  String.prototype.replaceAll = function (str, replace) {
    return replaceAll(str, replace, this)
  }
}

export const currentUserCan = (cap, collection, user, account) => {
  let hasCaps = false,
    hasCollection = false,
    hasLevel = false,
    hasModule = true
  let pathname = window.location.pathname.replace('/app/', '').replace('/app', '')
  let pathParts = pathname.split('/').filter(p => p)
  let iniPath = pathParts[0]
  let endPath = pathParts.reduce((a, b) => (b ? b : a), '')

  if (!isLoopableObject(AppModules)) {
    AppModules = require('Constants/AppModules')
  }

  user = user || localJSON.get('user', {})
  account = account || sessionJSON.get('account', false) || localJSON.get('account', false)
  !collection && iniPath in AppModules && (collection = iniPath)
  !AppModules[collection] &&
    (collection = Object.keys(AppModules).find(
      col => (collection && AppModules[col].route === collection) || (iniPath && AppModules[col].route === iniPath)
    ))

  const AppModule = AppModules[collection] || {}
  const { caps, level, module } = AppModule
  const subpath = pathname.replace(`${collection}/`, '').replace(`${collection}`, '')

  cap =
    cap ||
    (caps && caps[user.level] && caps[user.level].includes(endPath)
      ? endPath
      : !subpath || subpath === '/' || endPath === 'models'
        ? 'list'
        : endPath === 'add' || subpath.indexOf('/add') >= 0
          ? 'add'
          : 'edit')

  const { modules } = account || {}

  if (user.level && account) {
    hasCaps = user.level <= 2 || !caps || !caps[user.level] || !cap || caps[user.level].includes(cap)
    hasCollection = !collection || !(collection in (modules || {})) || (modules || {})[collection]
    hasModule = !module || !(module in (modules || {})) || (modules || {})[module]
    hasLevel = !level || level >= user.level
  }

  // console.groupCollapsed(`currentUserCan > ${collection}`);
  // console.log('account',account);
  // console.log('collection',collection);
  // console.log('cap',cap);
  // console.log('caps',caps);
  // console.log('level',level);
  // console.log('endPath',endPath);
  // console.log('subpath',subpath);
  // console.log('pathParts',pathParts);
  // console.log('hasCaps',hasCaps);
  // console.log('hasCollection',hasCollection);
  // console.log('hasModule',hasModule);
  // console.log('hasModule', { modules, module });
  // console.log('hasLevel',hasLevel);
  // console.groupEnd(`currentUserCan > ${collection}`);

  return Boolean(hasCaps && hasCollection && hasLevel && hasModule)
}

export const getAffiliateLink = (affiliate, append) => {
  const BASE_URL = process.env.CHECKOUT_APP_URL
  let affiliateLink = `${BASE_URL}?${URL_PARAMS_AFFILIATE_ID}=${affiliate.ID}`
  if (!!append && affiliateLink.indexOf(append) === -1) {
    affiliateLink += `${affiliateLink.split('').includes('?') ? '&' : '?'}${append}`
  }
  return affiliateLink
}

export const getAffiliateHomeLink = (affiliate, append) => {
  let affiliateLink = `${SITE_URL}/?${URL_PARAMS_AFFILIATE_HOME_ID}=${affiliate.ID}`
  if (!!append && affiliateLink.indexOf(append) === -1) {
    affiliateLink += `${affiliateLink.split('').includes('?') ? '&' : '?'}${append}`
  }
  return affiliateLink
}

export const getParentAccountLink = (account, append) => {
  const BASE_URL = process.env.CHECKOUT_APP_URL
  let parentLink = `${BASE_URL}?${URL_PARAMS_PARENT_ID}=${account.ID}`
  if (!!append && parentLink.indexOf(append) === -1) {
    parentLink += `${parentLink.split('').includes('?') ? '&' : '?'}${append}`
  }
  return parentLink
}

export const getVideoUrl = ({ collection, postId, filename, fileExt, ...other }) => {
  let videoUrl = ''
  let trackParams = []
  if (collection && postId && filename) {
    let c = COLLECTIONS.SHORT_URL_COLLECTIONS_PARAMS[collection] || collection
    if (!other.trigger) other.trigger = APP_TRIGGERS.APP_TRIGGER_PLAYED
    if (isLoopableObject(other)) {
      Object.keys(other).forEach(k => {
        trackParams.push(`${k}/${other[k]}`)
      })
    }
    videoUrl = `${CONSTANTS.VIDEO_TRACK_URL}${trackParams.join('/')}${trackParams.length ? '/' : ''}`
    videoUrl += `c/${c}/d/${postId}/v/${filename}${fileExt ? `.${fileExt.replace(/\./, '')}` : ''}`
  }
  return videoUrl
}

export const getBannerUrl = ({ collection, postId, filename, fileExt, ...other }) => {
  let bannerUrl = ''
  let trackParams = []
  if (collection && postId && filename) {
    let c = COLLECTIONS.SHORT_URL_COLLECTIONS_PARAMS[collection] || collection
    // if (!other.trigger) other.trigger = APP_TRIGGERS.APP_TRIGGER_IMPRESSION // "impression" is already default trigger in PHP
    if (isLoopableObject(other)) {
      Object.keys(other).forEach(k => {
        trackParams.push(`${k}/${other[k]}`)
      })
    }
    bannerUrl = `${CONSTANTS.BANNER_TRACK_URL}${trackParams.join('/')}${trackParams.length ? '/' : ''}`
    bannerUrl += `c/${c}/d/${postId}/i/${filename}.${(fileExt || 'png').replace(/\./, '')}`
  }
  return bannerUrl
}

export const getTrackUrl = ({ url, collection, postId, trigger, ...other }) => {
  let trackParams = []
  if (collection) {
    let c = COLLECTIONS.SHORT_URL_COLLECTIONS_PARAMS[collection] || collection
    trackParams.push(`c/${c}`)
  }
  if (postId) trackParams.push(`d/${postId}`)
  if (trigger) trackParams.push(`trigger/${trigger}`)
  if (isLoopableObject(other)) {
    Object.keys(other).forEach(k => {
      trackParams.push(`${k}/${other[k]}`)
    })
  }
  let trackUrl = `${CONSTANTS.LINK_TRACK_URL}${trackParams.join('/')}${trackParams.length ? '/' : ''}`
  if (url) trackUrl += `?url=${encodeURIComponent(url)}`
  return trackUrl
}

export const getBannerEmbedCode = ({ url, collection, postId, filename, fileExt, width, height }) => {
  let bannerUrl = getBannerUrl({ collection, postId, filename, fileExt })
  let trackUrl =
    !!url &&
    getTrackUrl({
      url,
      collection,
      postId,
      filename,
      trigger: APP_TRIGGERS.APP_TRIGGER_CLICKED,
    })
  let bannerMkp = `<img width="${width || ''}" height="${height || ''}" src="${bannerUrl}" alt="${filename}">`
  return trackUrl ? `<a href="${trackUrl}">${bannerMkp}</a>` : bannerMkp
}

export const getVideoEmbedCode = ({ collection, postId, filename, fileExt, width, height, poster }) => {
  let videoUrl = getVideoUrl({ collection, postId, filename, fileExt })
  let videoMkp = videoUrl
    ? `<video width="${width || ''}" height="${height || ''}" poster="${poster || ''}" alt="${filename}"><source src="${videoUrl}"></video>`
    : ''
  return videoMkp
}

export const getViewLink = (post, append) => {
  const { collection } = post
  let viewLink = ''
  switch (collection) {
    case COLLECTIONS.LANDING_PAGES_COLLECTION_NAME:
      if (post.config?.customDomain && post.config?.domain) {
        let {
          config: { domain },
        } = post
        viewLink = `https://${domain.replace('https://', '').replace('http://', '')}`
      } else if (post.permalink && post.ID) {
        viewLink = `${CONSTANTS.REMOTE_URL}/l/${post.ID}/`
      }
      break
    case COLLECTIONS.FORMS_COLLECTION_NAME:
      if (post.ID) {
        viewLink = `${CONSTANTS.REMOTE_URL}/${collection}/${post.ID}/`
      }
      break
    default:
      break
  }

  if (viewLink && !!append && viewLink.indexOf(append) === -1) {
    viewLink += `${viewLink.split('').includes('?') ? '&' : '?'}${append}`
  }

  return viewLink
}

export const getPlanUrl = (post, append) => {
  const { ID } = post
  let planUrl = qiplusPlansRoutes[ID]?.url ?? `${APP_URL}plans/${ID}`
  if (planUrl && !!append && planUrl.indexOf(append) === -1) {
    planUrl += `${planUrl.split('').includes('?') ? '&' : '?'}${append}`
  }
  return planUrl
}

export const getCkEditorConfigUrls = () => {
  const user = localJSON.get('user', false)
  const currentAccountID = sessionJSON.get('account', {}).ID || (user || {}).accountId
  const accountId = currentAccountID || user.accountId
  return {
    filebrowserBrowseUrl: `${CONSTANTS.REMOTE_URL}/ckfinder/ckfinder.html?Type=Files&userDir=${accountId}`,
    filebrowserImageBrowseUrl: `${CONSTANTS.REMOTE_URL}/ckfinder/ckfinder.html?Type=Images&userDir=${accountId}`,
    filebrowserUploadUrl: `${CONSTANTS.REMOTE_URL}/ckfinder/core/connector/php/connector.php?command=QuickUpload&type=Files&userDir=${accountId}`,
    filebrowserImageUploadUrl: `${CONSTANTS.REMOTE_URL}/ckfinder/core/connector/php/connector.php?command=QuickUpload&type=Images&userDir=${accountId}`,
  }
}

export const createPixelScript = ({ collection, postId, trigger, ...other }) => {
  let trackParams = []
  if (collection) {
    let c = COLLECTIONS.SHORT_URL_COLLECTIONS_PARAMS[collection] || collection
    trackParams.push(`c/${c}`)
  }
  if (postId) trackParams.push(`d/${postId}`)
  if (trigger) trackParams.push(`trigger/${trigger}`)
  if (isLoopableObject(other)) {
    Object.keys(other).forEach(k => {
      trackParams.push(`${k}/${other[k]}`)
    })
  }
  let pixelUrl = `${CONSTANTS.BANNER_TRACK_URL}${trackParams.join('/')}${trackParams.length ? '/' : ''}pixel.png`
  const script = `<script>
(function(a,c) {
    var d=document, img = d.createElement('img');
    img.src = '${pixelUrl}';
    if (document.referrer) img.src += '?referrer='+encodeURIComponent(document.referrer);
    if (location.href) img.src += '&url='+encodeURIComponent(location.href);
    d.body.appendChild(img);
}());
</script>`
  /*
<script>
(function(s,a) {
    function p(params) {
        var d=document, img = d.createElement('img');
        img.src = 'https://qiplus.com.br/track/p/c/c/d/UiIdbGHIqddTBzBTZkmY/a/'+a+'/pixel.png';
        if (document.referrer) img.src += '?referrer='+document.referrer;
        if (params) img.src += params;
        d.body.appendChild(img);
    }
    try {
        fetch('https://www.cloudflare.com/cdn-cgi/trace').then(r=>r.text())
        .then(t=>p('&ip='+(t.split('\n').find(p=>p.split('=')[0]=='ip')||'').split('=')[1]||''))
    } catch (err) {p()}
}('K0neCRFHCFgmmyx3wGkEPm5XRbi2'));
</script>
*/
  return script
}

export const createAcceptanceScript = ({ accountId, trigger, policyType, policyBaseUrl, acceptanceText, ...other }) => {
  const ipApiUrl = 'https://www.cloudflare.com/cdn-cgi/trace'
  const bgColor = other.bgColor || '#FFFFFF'
  const color = other.color || '#000000'
  const btnBgColor = other.btnBgColor || '#108bea'
  const btnColor = other.btnColor || '#FFFFFF'
  const script = `<script>
(function(a,c) {
    var d=document,style=d.createElement('style'),div=d.createElement('div');style.innerHTML='#qiplus-policy-accept{display:none;box-shadow:0 0 10px rgba(0,0,0,.5);position:fixed;bottom:0;width:100%;background:${bgColor};color:${color};font-family:sans-serif;font-size:16px;text-align:center;padding:2em 1em;z-index:1;box-sizing:border-box;}#qiplus-policy-accept>p{color:${color};}#qiplus-policy-btn{display:inline-block;padding:6px 12px;margin-bottom:0;font-size:14px;font-weight:400;line-height:1.********;text-align:center;white-space:nowrap;vertical-align:middle;-ms-touch-action:manipulation;touch-action:manipulation;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;background-image:none;border:1px solid transparent;border-radius:4px;background:${btnBgColor};color:${btnColor};border:none;font-size:14px;line-height:22px;border-radius:4px;}';div.innerHTML='<p>${acceptanceText}</p><span id="qiplus-policy-btn">Aceitar</span>';div.id='qiplus-policy-accept';d.head.appendChild(style);d.body.appendChild(div);var sp={};if(d.referrer)sp.referrer=d.referrer;if(navigator.userAgent)sp.user_agent=navigator.userAgent;var cb=function(ip){if(ip){sp.remote_addr=ip}};fetch("${ipApiUrl}").then(function(r){r.text().then(function(r){cb(r.match(/[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}/)[0])})}).catch(console.error);var a=function(){var dt={id:'${accountId}',accountId:'${accountId}',trigger:'${trigger}',enviroinment:'${APP_ENVIROINMENT}'};Object.keys(sp).forEach(function(k,i){!dt[k]&&(dt[k]=sp[k])});fetch('${CONSTANTS.REMOTE_URL}/${policyBaseUrl}/?a=${accountId}&enviroinment=${APP_ENVIROINMENT}',{method:'post',headers:{'Content-Type':'application/json'},body:JSON.stringify(dt)}).catch(console.error)};var pId='policy-${policyType}-${accountId}';if(!localStorage.getItem(pId)){d.getElementById('qiplus-policy-accept').style.display='block'}var pBtn=d.getElementById('qiplus-policy-btn');pBtn.addEventListener('click',function(){localStorage.setItem(pId,1);d.getElementById('qiplus-policy-accept').style.display='none';a()})
}());
</script>`
  return script
}

export const createPrivacyPolicyScript = ({ accountId, ...other }) => {
  const trigger = APP_TRIGGERS.APP_TRIGGER_PRIVACY_POLICY_ACCEPTED
  const policyType = 'privacy-policy'
  const policyBaseUrl = 'privacy-policy'
  const acceptanceText = 'Ao continuar a navegar no site concorda com a nossa <b>Política de Privacidade</b>'
  const script = createAcceptanceScript({
    accountId,
    trigger,
    policyType,
    policyBaseUrl,
    acceptanceText,
    ...other,
  })

  return script
}

export const createTermsOfUseScript = ({ accountId, ...other }) => {
  const trigger = APP_TRIGGERS.APP_TRIGGER_TERMS_OF_USE_ACCEPTED
  const policyType = 'terms-of-use'
  const policyBaseUrl = 'terms-of-use'
  const acceptanceText = 'Ao continuar a navegar no site concorda com os nossos <b>Termos de Uso</b>'
  const script = createAcceptanceScript({
    accountId,
    trigger,
    policyType,
    policyBaseUrl,
    acceptanceText,
    ...other,
  })

  return script
}

export const createGoogleFormsScript = (formID, webhook) => {
  const fnStr = `function onFormSubmit(e) {

    var formID = '${formID}';
    var url = '${webhook}';

    // Open a form by ID and log the responses to each question.
    var form = FormApp.openById(formID); // https://docs.google.com/forms/d/FormID/edit
    var formResponses = form.getResponses();
    var formResponse = formResponses[formResponses.length - 1];
    var itemResponses = formResponse.getItemResponses();
    var customerNameresponse = itemResponses[0];
    var email = formResponse.getRespondentEmail();

    var data = {
        formID: formID,
        email: email,
        responses: [],
        spreadsheets: 'https://docs.google.com/spreadsheets/d/<Google SpreadsheetID>/edit?usp=sharing',
    };

    itemResponses.forEach(function(item, index, array){
        data.responses[index] = {
            field: item.getItem().getTitle(),
            value: item.getResponse(),
        }
    });

    var options = {
        'method': 'post',
        'headers': {
            'Content-Type': 'application/json'
        },
        'payload': JSON.stringify(data)
    };

    var response = UrlFetchApp.fetch(url, options);
        Logger.log(response.getContentText());

}`
  return fnStr
}

export const createWebhook = (platform, accountId, integrationId) =>
  `${CONSTANTS.WEBHOOKS_URL}/${platform}/?accountId=${accountId}&integrationId=${integrationId}`

export const createShortlink = longUrl => {
  return new Promise((resolve, reject) => {
    let token = CONSTANTS.BITLY_TOKEN
    let config = {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    }
    let data = { domain: 'bit.ly', long_url: longUrl }

    axios
      .post('https://api-ssl.bitly.com/v4/shorten', JSON.stringify(data), config)
      .then(res => {
        console.log('res', res)
        if ((res.status === 200 || res.status === 201) && (res.data || {}).link) {
          let {
            data,
            data: { link },
          } = res
          console.log(link)
          return resolve(data)
        }
        return resolve({ error: 'unknown' })
        // console.log(res.data)
      })
      .catch(error => {
        console.log(error)
        return resolve({ error })
      })
  })
}

export const createEmbed = function (url, name = 'frame', width = '40%', height = '550px') {
  const embed = `<iframe src=${url} name=${name} scrolling="auto" width=${width} height=${height}></iframe>`
  return embed
}

export const encodeMessage = str =>
  btoa(
    encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function (match, p1) {
      return String.fromCharCode('0x' + p1)
    })
  )
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '')

export const b64DecodeUnicode = str => {
  // Going backwards: from bytestream, to percent-encoding, to original string.
  return decodeURIComponent(
    atob(str.replace(/-/g, '+').replace(/_/g, '/'))
      .split('')
      .map(function (c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
      })
      .join('')
  )
}

export const insertScripts = (sources, onLoad) => {
  if (!Array.isArray(sources)) sources = [sources]
  sources.forEach((src, i) => {
    const script = document.createElement('script')
    script.src = src
    if (onLoad && i === sources.length - 1) script.onload = onLoad
    document.body.appendChild(script)
  })
}

export const getBodyAttributeValue = (attrName, el) => {
  let attr = document.body.attributes.getNamedItem(attrName)
  return (attr || {}).value || ''
}

export const isValidDomain = str => {
  const domain = str.replace('https://', '').replace('http://', '')
  const domainRegexp = /^[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,6}$/i
  return domainRegexp.test(domain) ? true : false
}

export const sortByDate = (a, b, order) => {
  let A = order === 'asc' ? b : a
  let B = order === 'asc' ? a : b
  return A.createdAt && B.createdAt ? B.createdAt - A.createdAt : !(A.date && B.date) ? 0 : new Date(B.date) - new Date(A.date)
}

export const sortByUpdateDate = (a, b, order, key) => {
  let A = order === 'asc' ? b : a
  let B = order === 'asc' ? a : b
  return A[key] && B[key]
    ? new Date(B[key]) - new Date(A[key])
    : A.updatedAt && B.updatedAt
      ? B.updatedAt - A.updatedAt
      : A.modified && B.modified
        ? new Date(B.modified) - new Date(A.modified)
        : A.createdAt && B.createdAt
          ? B.createdAt - A.createdAt
          : A.date && B.date
            ? new Date(B.date) - new Date(A.date)
            : 0
}

export const paginateArray = (array, pageSize) => {
  pageSize = pageSize || 10
  let pages = []
  let page = []
  Array.isArray(array) &&
    array.forEach((a, i) => {
      page.push(a)
      if (page.length === pageSize || i === array.length - 1) {
        pages.push(page)
        page = []
      }
    })
  return pages
}

export function sortMatch(value, array, sortKey) {
  if (!isDefined(value) || !isDefined(array) || !Array.isArray(array)) return array

  value = value.toString().toLowerCase().replaceAll(' ', '')

  const sortMatchFn = (vA, vB) => {
    var valA = vA
    var valB = vB

    if (typeof vA === 'object' && typeof vB === 'object' && sortKey in vA && sortKey in vB) {
      valA = vA[sortKey]
      valB = vB[sortKey]
    }

    var matchesA = 0
    var matchesB = 0

    valA = valA && valA.toString ? valA.toString().toLowerCase().replaceAll(' ', '') : ''
    valB = valB && valB.toString ? valB.toString().toLowerCase().replaceAll(' ', '') : ''

    if (valA === value) {
      matchesA += 100
    }

    if (valA.indexOf(value) === 0) {
      matchesA += 50
    } else if (valA.indexOf(value) > 0) {
      matchesA += 25
    }

    if (valB === value) {
      matchesB += 100
    }

    if (valB.indexOf(value) === 0) {
      matchesB += 50
    } else if (valB.indexOf(value) > 0) {
      matchesB += 25
    }

    var lengthAMatch = valA.length - value.length
    var lengthBMatch = valB.length - value.length

    if (lengthAMatch < lengthBMatch) {
      matchesA += 10
    } else if (lengthBMatch < lengthAMatch) {
      matchesB += 10
    }

    for (var s = 0; s < value.length; s++) {
      if (valA.length <= s && value[s] === valA[s]) matchesA += 5
      if (valB.length <= s && value[s] === valB[s]) matchesB += 5
    }

    if (valA.indexOf(value) < valB.indexOf(value)) {
      matchesA += 5
    } else if (valB.indexOf(value) < valA.indexOf(value)) {
      matchesB += 5
    }

    for (var s = 0; s < valA.length; s++) {
      if (hasStr(value, valA[s])) matchesA++
    }
    for (var s = 0; s < valB.length; s++) {
      if (hasStr(value, valB[s])) matchesB++
    }

    return matchesA < matchesB ? 1 : matchesA > matchesB ? -1 : 0
  }

  array.sort(sortMatchFn)
}

export function sortObjArrayByText(array, key, order) {
  if (Array.isArray(array))
    array.sort(function (a, b) {
      var textA, textB

      try {
        if (order === 'desc') {
          textA = b[key]
          textB = a[key]
        } else {
          textA = a[key]
          textB = b[key]
        }

        return textA < textB ? -1 : textA > textB ? 1 : 0
      } catch (err) {
        console.log(err)
        return 0
      }
    })

  return array
}

export function sortObjArrayByNum(array, key, order) {
  if (Array.isArray(array))
    array.sort(function (a, b) {
      var numA = Number(a[key])
      var numB = Number(b[key])

      try {
        if (order == 'desc') {
          return numB - numA
        } else {
          return numA - numB
        }
      } catch (err) {
        console.log(err)
        return 0
      }
    })

  return array
}

export function sortObjArrayAsc(array, key) {
  if (Array.isArray(array)) {
    return array
  }

  array.sort(function (a, b) {
    var textA = a[key]
    var textB = b[key]
    return textA < textB ? -1 : textA > textB ? 1 : 0
  })
}

export function nextObjItem(obj, currKey) {
  var nextKey = ''
  var nextVal = ''
  var isPrev = false

  for (var key in obj) {
    if (isPrev) nextKey = key
    if (isPrev) nextVal = obj[key]
    if (isPrev) break
    if (key == currKey) isPrev = true
  }

  return { key: nextKey, val: nextVal }
}

export function prevObjItem(obj, currKey) {
  var prevKey = ''
  var prevVal = ''
  var isPrev = false

  for (var key in obj) {
    if (key == currKey) break
    prevKey = key
    prevVal = obj[key]
  }

  return { key: prevKey, val: prevVal }
}

export function sTrim(string, charToRemove, returnFalse) {
  string = stringfy(string)
  charToRemove = charToRemove || ' '
  var iniLength = string.length
  returnFalse = returnFalse === true

  if (!Array.isArray(charToRemove)) charToRemove = [charToRemove]

  for (var i = charToRemove.length - 1; i >= 0; i--) {
    if (Array.isArray(charToRemove[i])) {
      if (charToRemove[i].length == 2) {
        if (
          (string.match(new RegExp(charToRemove[i][0], 'g')) && !string.match(new RegExp(charToRemove[i][1], 'g'))) ||
          (string.match(new RegExp(charToRemove[i][1], 'g')) && !string.match(new RegExp(charToRemove[i][0], 'g')))
        ) {
          if (returnFalse) return false
        } else if (string.match(new RegExp(charToRemove[i][0], 'g')) && string.match(new RegExp(charToRemove[i][1], 'g'))) {
          if (string.match(new RegExp(charToRemove[i][0], 'g')).length !== string.match(new RegExp(charToRemove[i][1], 'g')).length) {
            if (returnFalse) return false
          } else if (!returnFalse) {
            while (string.charAt(0).match(new RegExp(charToRemove[i][0])) && string.charAt(string.length - 1).match(new RegExp(charToRemove[i][1]))) {
              string = string.substring(1)
              string = string.substring(0, string.length - 1)
            }
          }
        }
      }

      charToRemove.splice(i, 1)
    }
  }

  if ((string = lTrim(string, charToRemove, returnFalse))) {
    string = rTrim(string, charToRemove, returnFalse)
  }

  return string
}

export function lTrim(string, charToRemove, returnFalse) {
  string = stringfy(string)
  charToRemove = charToRemove || ' '
  var iniLength = string.length
  returnFalse = returnFalse === true

  if (charToRemove == 'NaN') {
    while (Array.isArray(string.charAt(0).match(/[^0-9\.]+/g))) {
      string = string.substring(1)
    }
  } else {
    if (!Array.isArray(charToRemove)) charToRemove = [charToRemove]

    while (arrayHasVal(charToRemove, string.charAt(0))) {
      string = string.substring(1)
    }
  }

  // console.log('lTrim:---->>>>>>>>' + string);
  // console.log('----------------------------');

  if (returnFalse && string.length !== iniLength) {
    return false
  } else {
    return string
  }
}

export function rTrim(string, charToRemove, returnFalse) {
  string = stringfy(string)
  charToRemove = charToRemove || ' '
  var iniLength = string.length
  returnFalse = returnFalse === true

  if (charToRemove == 'NaN') {
    while (Array.isArray(string.charAt(string.length - 1).match(/[^0-9\.]+/g))) {
      string = string.substring(0, string.length - 1)
    }
  } else {
    if (!Array.isArray(charToRemove)) charToRemove = [charToRemove]

    while (arrayHasVal(charToRemove, string.charAt(string.length - 1))) {
      string = string.substring(0, string.length - 1)
    }
  }

  // console.log('rTrim:---->>>>>>>>' + string);
  // console.log('----------------------------');

  if (returnFalse && string.length !== iniLength) {
    return false
  } else {
    return string
  }
}

// grabHereToCopyPaste
const utf8BytesSize = str => {
  // Matches only the 10.. bytes that are non-initial characters in a multi-byte sequence.
  if (Array.isArray(str)) str = str.toString()
  if (typeof str === 'number' && str.toString) str = str.toString()
  if (typeof str !== 'string') return 0
  try {
    var m = encodeURIComponent(str).match(/%[89ABab]/g)
    return str.length + (m ? m.length : 0)
  } catch (error) {
    return str.length
  }
}

export const removeSpaces = string => stringfy(string).replace(/ /g, '')

export const toCammelCase = string =>
  stringfy(string)
    .split('_')
    .map((s, i) => (i === 0 && s) || s.charAt(0).toUpperCase() + s.substr(1, s.length))
    .join('')

export function hasStr(string, subString, matchAll) {
  if (!isDefined(subString) || !isDefined(string)) return false

  matchAll = matchAll === true
  string = stringfy(string)

  if (!Array.isArray(subString)) {
    subString = subString.split('[SEP]')
  }

  var strCheck = ''

  for (var i = 0; i < subString.length; i++) {
    var thisSubString = stringfy(subString[i])

    if (isDefined(thisSubString) && string.indexOf(thisSubString) >= 0) {
      if (!matchAll) return true
      strCheck += 'true'
    } else {
      if (matchAll) return false
      strCheck += 'false'
    }
  }

  return strCheck.indexOf('true') >= 0
}

export function isDefined(variable, acceptNull, acceptEmpty, acceptZero, acceptNaN) {
  if (acceptNull === true && variable === null) return true
  if (acceptEmpty === true && variable === '') return true
  if (acceptZero === true && variable === 0) return true

  if (isNaN(variable) && typeof variable === 'number' && acceptNaN !== true) return false // NaN returns number from typeof

  return variable !== undefined && variable !== null && variable !== '' && variable !== 0
}

export const diffObjects = (leftObj, rightObj, maxDepth, matchPos, matchString, level, key) => {
  level = level || 0
  maxDepth = maxDepth || 1
  if (level > maxDepth) return leftObj
  if (areEqualObjects(leftObj, rightObj, matchPos, key)) {
    // console.log('diffObjects > areEqualObjects > case: 1', window.jsonClone({key,leftObj,rightObj}));
    return null
  }
  if (typeof leftObj !== typeof rightObj) {
    // console.log('diffObjects > no match case: 2', window.jsonClone({key,leftObj,rightObj}));
    if (matchString === false && ![typeof leftObj, typeof rightObj].find(t => t !== 'number' && t !== 'string')) {
      if (isDefined(leftObj, false, false, true) && isDefined(rightObj, false, false, true) && parseFloat(leftObj) === parseFloat(rightObj)) {
        return null
      }
    }
    return isDefined(leftObj, true, true, true) ? leftObj : null
  }
  if ([typeof leftObj, typeof rightObj].includes('function')) {
    // console.log('diffObjects > function > case: 3', window.jsonClone({key,leftObj,rightObj}));
    return typeof leftObj === 'function' ? leftObj : null
  }
  if (isEmptyObject(leftObj) && isEmptyObject(rightObj)) {
    // console.log('diffObjects > emptyObjects  > case: 4', window.jsonClone({key,leftObj,rightObj}));
    return {}
  }
  if (!isLoopable(leftObj) || !isLoopable(rightObj)) {
    // console.log('diffObjects > !loopable > case: 5', window.jsonClone({key,leftObj,rightObj}));
    return isDefined(leftObj, true, true, true) ? leftObj : null
  }
  let diffObject = Array.isArray(leftObj) ? [] : {}
    ;[...new Set([...Object.keys(leftObj), ...Object.keys(rightObj)])].forEach(k => {
      if (!isCyclic(leftObj[k]) && !isCyclic(rightObj[k])) {
        let res = diffObjects(leftObj[k], rightObj[k], maxDepth, matchPos, matchString, level + 1, k)
        let posLeft = Object.keys(leftObj).indexOf(k)
        let posRight = Object.keys(rightObj).indexOf(k)
        if (res !== null || (matchPos === true && posLeft !== posRight)) {
          Array.isArray(diffObject) ? diffObject.push(res) : (diffObject[k] = res)
          // console.log('diffObjects > no match case: 6', window.jsonClone({key,k,res,leftObj:leftObj[k], rightObj:rightObj[k]}));
        }
      } else {
        // console.log('diffObjects > isCyclic', window.jsonClone({key,k,leftObj:leftObj[k], rightObj:rightObj[k]}))
      }
    })
  return isLoopable(diffObject) ? diffObject : null
}

export const areEqualObjects = (obj1, obj2, matchPos, key) => {
  if (typeof obj1 !== typeof obj2) {
    // console.log('areEqualObjects > no match case: 1', window.jsonClone({key,obj1,obj2}));
    return false
  }
  if ([typeof obj1, typeof obj2].includes('function')) {
    // if (typeof obj1 !== typeof obj2) console.log('areEqualObjects > no match case: 2', window.jsonClone({key,obj1,obj2}));
    return typeof obj1 === typeof obj2
  }
  if (isEmptyObject(obj1) && isEmptyObject(obj2)) {
    // console.log('match!! > isEmptyObject', window.jsonClone({key,obj1,obj2}));
    return true
  }
  if (!isLoopable(obj1) || !isLoopable(obj2)) {
    // if (obj1!==obj2) console.log('areEqualObjects > no match case: 3', window.jsonClone({key,obj1,obj2}));
    return obj1 === obj2
  }
  return [...new Set([...Object.keys(obj1), ...Object.keys(obj2)])].reduce((r, k) => {
    if (isCyclic(obj1[k]) || isCyclic(obj2[k])) {
      // console.log('areEqualObjects > isCyclic', {key, k}, window.jsonClone({obj1:obj1[k], obj2:obj2[k]}))
      return r
    }
    let pos1 = Object.keys(obj1).indexOf(k)
    let pos2 = Object.keys(obj2).indexOf(k)
    let match = areEqualObjects(obj1[k], obj2[k], matchPos, k) && (matchPos !== true || pos1 === pos2)
    // if (!match) console.log('areEqualObjects > no match case: 4', {key, k}, window.jsonClone({obj1:obj1[k], obj2:obj2[k]}));
    return r && match
  }, true)
}

export const filterRecursively = (arr, finder, keys) => {
  keys = Array.isArray(keys) ? keys : null
  let all = arr.filter(finder)
  arr.forEach(
    a =>
      isLoopable(a) &&
      (keys || Object.keys(a))
        .filter(c => Array.isArray(a[c]))
        .forEach(c => {
          all = [...all, ...filterRecursively(a[c], finder, keys)]
        })
  )
  return all
}

export const getDeepValue = (obj, keys, defaultVal) => {
  let v
  if (isset(obj, keys)) {
    let test = JSON.parse(JSON.stringify(obj))
    keys.forEach(k => {
      test = test[k]
    })
    v = test
  }
  return defaultVal === undefined ? v : isDefined(v, true, true, true) ? v : defaultVal
}

export const isset = (obj, keys) => {
  let failed = false
  if (isLoopable(obj)) {
    try {
      let test = JSON.parse(JSON.stringify(obj))
      keys.forEach(k => {
        if (!failed && k in test) {
          test = test[k]
        } else failed = true
      })
      return !failed
    } catch (error) {
      return false
    }
  }
  return false
}

let delayLogTimeout,
  delayLogStack = []
export const delayLog = (obj, key, timeout) => {
  let index = delayLogStack.findIndex(o => o.key === key && (!key || areEqualObjects(o.obj, obj)))
  if (index === -1) delayLogStack.push({ obj, key })
  else delayLogStack[index] = { obj, key }
  clearTimeout(delayLogTimeout)
  delayLogTimeout = setTimeout(() => {
    delayLogStack.forEach(d => console.log(d.key || '', d.obj))
    delayLogStack = []
  }, timeout || 1000)
}

export function isInt(val) {
  if (!isDefined(val, true) || isNaN(val)) return false
  return val === parseInt(val, 10)
}

export function isNumberCharCode(event) {
  event = event ? event : window.event
  var charCode = event.which ? event.which : event.keyCode
  if ((charCode >= 48 && charCode <= 57) || (charCode >= 96 && charCode <= 105)) {
    return true
  }
  return false
}

export const isNumeric = value => {
  //Validate Number values
  if (hasStr(value, ',')) {
    try {
      let pieces = value.toString().split(',')
      if (pieces.length === 2 && (value.toString().indexOf('.') === -1 || value.toString().indexOf(',') > value.toString().indexOf('.'))) {
        value = pieces[0].replaceAll('.', '') + '.' + pieces[1]
      } else if (value.toString().split('.').length === 2 && value.toString().indexOf('.') > value.toString().indexOf(',')) {
        value = value.replaceAll(',', '')
      }
    } catch (err) { }
  }
  return ['bigint', 'number', 'string'].includes(typeof value) && !isNaN(Number(value))
}

export const number_format = (number, decimals, dec_point, thousands_sep) => {
  number = String(number || '').replace(/[^0-9+\-Ee.]/g, '')
  var n = !isFinite(Number(number)) ? 0 : Number(number),
    prec = !isFinite(Number(decimals)) ? 0 : Math.abs(decimals),
    sep = typeof thousands_sep === 'undefined' ? ',' : thousands_sep,
    dec = typeof dec_point === 'undefined' ? '.' : dec_point,
    s = '',
    toFixedFix = function (n, prec) {
      var k = Math.pow(10, prec)
      return String((Math.round(n * k) / k).toFixed(prec))
    }
  // Fix for IE parseFloat(0.55).toFixed(0) = 0;
  s = (prec ? toFixedFix(n, prec) : String(Math.round(n))).split('.')
  if (s[0].length > 3) {
    s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep)
  }
  if ((s[1] || '').length < prec) {
    s[1] = s[1] || ''
    s[1] += new Array(prec - s[1].length + 1).join('0')
  }
  return s.join(dec)
}

export function stringfy(target, separator, returnEmptyifNotValid) {
  if (target == null || target == undefined) {
    target = ''
  } else if (Array.isArray(target)) {
    if (!isDefined(separator)) separator = ' '
    target = target.join(separator)
  } else if (typeof target === 'object') {
    target = JSON.stringify(target)
  } else {
    try {
      target = target.toString()
    } catch (err) {
      console.log(err)
      if (returnEmptyifNotValid === true) target = ''
    }
  }

  return target
}

export const convertToBoolean = val => {
  val = typeof val === 'string' && !isNaN(Number(val)) && val !== '' ? Number(val) : val
  val = typeof val === 'number' ? (val === 0 ? false : val === 1 ? true : val) : val
  return Boolean(val)
}

export const convertTonumOnly = val => {
  val = typeof val === 'number' ? val : typeof val === 'string' ? val.replace(/[^\d]/g, '') : val
  return val
}

export const toCents = n =>
  parseInt(
    Number(n || 0)
      .toFixed(2)
      .toString()
      .replace(/\D/g, ''),
    10
  )

export const capitalizeStr = s => {
  if (typeof s !== 'string') return s
  return s.charAt(0).toUpperCase() + s.slice(1)
}

export function currencyFormat(number, userLocale) {
  if (!userLocale) userLocale = document.getElementsByTagName('body')[0].getAttribute('lang') || navigator.language || navigator.userLanguage

  // var options = {style: 'currency', currencyDisplay: 'symbol', currency: currency[userLocale], minimumFractionDigits: 2, maximumFractionDigits: 2};
  var options = { minimumFractionDigits: 2, maximumFractionDigits: 2 }
  var formatter = new Intl.NumberFormat(userLocale, options)

  return formatter.format(number)
}

export function randomInt(min, max) {
  min = min || 1
  max = max || 999999999999999999999
  return Math.floor(Math.random() * (max - min + 1) + min)
}

export function numberFormat(number, userLocale) {
  if (!isDefined(userLocale))
    userLocale = document.getElementsByTagName('body')[0].getAttribute('lang') || navigator.language || navigator.userLanguage

  var formatter = new Intl.NumberFormat(userLocale)

  return formatter.format(number)
}

export function animateVal(mixed, val, iniVal) {
  var callback = typeof mixed === 'function' ? mixed : null

  var target = typeof mixed === 'string' ? document.querySelectorAll(mixed)[0] : null

  if (!target && !callback) return

  var isDecimal = val > 0 && val < 1
  var increaser = typeof iniVal === 'number' ? iniVal : 0
  var range = parseFloat(val) - increaser

  var step = 0
  var steps = isDecimal || range > 1000 ? 100 : 10
  var stepVal = isDecimal ? val * 100 : range / steps

  var animationIntval = setInterval(function () {
    step++
    increaser += Number(stepVal) + (isDecimal ? 0 : 1)
    var displayVal = isDecimal
      ? '0.' + Number(increaser / 100).toFixed(0)
      : val - parseInt(val) != 0
        ? Number(increaser).toFixed(2)
        : Number(increaser).toFixed(0)

    if (step >= steps) {
      displayVal = val
      clearInterval(animationIntval)
    }

    if (typeof callback === 'function') {
      callback(displayVal)
      return
    }

    target.innerText = displayVal
  }, 10)
}

export function clipboardCopy(val, cb) {
  const innerCb = () => {
    switch (typeof cb) {
      case 'string':
        alert(cb)
        break
      case 'function':
        cb()
        break
      default:
        break
    }
  }
  const isOS = () => navigator.userAgent && navigator.userAgent.match(/ipad|iphone/i)
  const fallback = () => {
    var textArea = document.createElement('textarea')
    textArea.style.opacity = '0'
    textArea.style.position = 'fixed'
    try {
      textArea.value = val.toString()
      document.body.appendChild(textArea)
      /* Select the text field */
      if (isOS()) {
        var range, selection
        range = document.createRange()
        range.selectNodeContents(textArea)
        selection = window.getSelection()
        selection.removeAllRanges()
        selection.addRange(range)
        textArea.setSelectionRange(0, 999999)
      } else {
        textArea.select()
      }
      /* Copy the text inside the text field */
      var successful = document.execCommand('copy')
      /* callback */
      if (successful) {
        innerCb()
      } else {
        prompt('', val)
      }
      /* Remove the form */
      document.body.removeChild(textArea)
    } catch (err) {
      prompt('', val)
    }
  }
  // copy text to clipboard
  if (window.navigator.clipboard && navigator.clipboard.writeText) {
    navigator.clipboard.writeText(val).then(innerCb).catch(fallback)
  } else {
    fallback()
  }
}

export function matchCEP(cep, val1, val2) {
  var match = false

  val1 = val1.toString().replace(/\D/g, '')
  val2 = val2.toString().replace(/\D/g, '')
  cep = cep.toString().replace(/\D/g, '')

  var val1ToStr = sTrim(val1.toString().split('-')[0]).substr(0, 5)
  var val2ToStr = sTrim(val2.toString().split('-')[0]).substr(0, 5)
  var cepToStr = sTrim(cep.toString().split('-')[0]).substr(0, 5)

  val1 = parseFloat(val1ToStr)
  val2 = parseFloat(val2ToStr)
  cep = parseFloat(cepToStr)

  match = val1 <= cep && cep <= val2

  return match
}

export function checarBairro(jsonObj, cepInput, displayAlert) {
  // console.log('checarBairro ----------------------------------------------------------');

  // console.log(jsonObj);

  displayAlert = displayAlert === true

  if (!isDefined(cepInput)) {
    cepInput = $('input#cep')
  }

  var thisForm = cepInput.closest('table,form')

  if ('erro' in jsonObj) {
    if (displayAlert) return textosLang[currentLanguage]['alert_cep_invalido'], ''
  } else if (typeof jsonObj === 'object' && 'bairro' in jsonObj) {
    var bairro = jsonObj['bairro']
    var rua = jsonObj['logradouro']
    var cidade = jsonObj['localidade']
    var estado = jsonObj['uf']

    thisForm.find('input.bairro').val(bairro)
    thisForm.find('input.rua').val(rua)
    thisForm.find('input.cidade').val(cidade)
    thisForm.find('input.estado').val(estado)

    thisForm.find('input.numero').val('')
    thisForm.find('input.complemento').val('')
    thisForm.find('input.referencia').val('')
  }
}

export function formatarCEP(cep) {
  cep = sTrim(cep).toString().replace(/[^\d]/g, '')
  var cepFormatado = cep

  // console.log(cepFormatado);
  if (cepFormatado.length == 8) {
    for (var i = 0; i < cep.length; i++) {
      if (i === 5) cepFormatado = cepFormatado.substring(0, i) + '-' + cepFormatado.substring(i, 8)
    }
  }

  return cepFormatado
}

export function validateCEP(cep) {
  cep = sTrim(cep).toString().replace(/\D/g, '')
  return cep.toString().length === 8
}

export async function checkCep(cep) {
  return Axios.get('https://viacep.com.br/ws/' + cep + '/json/').then((res) => {
    return res
  })
}

export function consultaCEP(cep) {
  // console.log('consultaCEP ----------------------------------------------------------');

  return new Promise((res, rej) => {
    cep = sTrim(cep).toString().replace(/\D/g, '')

    if (!isDefined(cep)) return rej({ error: 'undefined CEP' })

    if (cep.toString().length === 5) {
      cep = cep + '000'
    }

    if (cep.toString().length !== 8) {
      return rej({
        error: textosLang[currentLanguage]['confirm_cep_invalido'],
      })
    }

    var viaCEPURL = 'https://viacep.com.br/ws/' + cep + '/json/'
    // console.log(viaCEPURL);

    if (typeof $ === 'function' && $.ajax) {
      $.ajax({
        type: 'GET', // this is the essence of jsonp
        url: viaCEPURL,
        cache: false, // to ensure proper data response
        dataType: 'jsonp', // jsonp
        crossDomain: true, // enable ssl/nonssl
        data: {
          format: 'json',
        },

        // Work with the response
        success: function (response) {

          if ('erro' in response) {
            return rej({
              error: textosLang[currentLanguage]['alert_cep_invalido'],
            })
          }

          return res(response)
        },
        error: function (err) {
          console.log('err', err)
          return rej({ err, error: textosLang[currentLanguage]['error_ajax'] })
        },
      })
    } else {
      return rej({
        error: textosLang[currentLanguage]['error_ajax_unsuportted'],
      })
    }
  })
}

export function FirestoreBatchHandler(workRef, options, intents) {
  options = options || {}
  intents = intents || 0

  let { limit, onTransaction, verbose, showHidden, depth } = options

  let refBatch = workRef.batch()
  let writeInterval = options.writeInterval || 180

  this.transactionLimit = limit || 500
  this.writeLimit = Math.max(this.transactionLimit, options.writeLimit || 5000)

  this.commited = false
  this.counter = 0
  this.partialCounter = options.partialCounter || 0
  this.totalCounter = 0
  this.timeCount = 0
  this.timerInterval = null
  this.stack = []
  this.handlerBatches = []
  this.pending = []
  this.waiting = Boolean(options.waiting)

  this.transactions = {
    set: 0,
    update: 0,
    delete: 0,
    commits: 0,
    all: [],
  }

  let transactions = JSON.parse(JSON.stringify(this.transactions))

  this.log = data => {
    if (verbose === false) return this
    if (typeof log !== 'function') var log = ''
    let logger = typeof log === 'function' ? obj => log(obj, showHidden, depth) : console.log
    logger(data)
    return this
  }

  this.logTx = (tx, other) => {
    if (verbose === false) return this
    let res = { ...(other || {}) }
    Object.keys(tx).forEach((k, i) => {
      let l = Array.isArray(tx[k]) ? tx[k].length : tx[k]
      l && (res[k] = l)
    })
    this.log(res)
    return this
  }

  this.delete = (docRef, data, txCb) => {
    this.onExecute('delete', docRef, data, txCb)
    return this
  }
  this.update = (docRef, data, txCb) => {
    this.onExecute('update', docRef, data, txCb)
    return this
  }
  this.set = (docRef, data, txCb) => {
    this.onExecute('set', docRef, data, txCb)
    return this
  }

  this.startTimer = int => {
    this.timerInterval = setInterval(() => {
      this.timeCount++
      if (this.timeCount % 30 === 0) this.log(`--waiting at ${intents > 0 ? `atempt ${intents}` : ''} ${this.timeCount}/${writeInterval}s`)
      if (this.timeCount === writeInterval) this.timerEnd()
    }, int || 1000)
    return this
  }

  this.timer = () => {
    this.log(`--waiting at ${this.totalCounter} transactions`)
    this.waiting = true
    this.startTimer()
    return this
  }

  this.timerEnd = () => {
    this.log(`--timerEnd: restarted at ${this.totalCounter} transactions`)
    clearInterval(this.timerInterval)

    this.timeCount = 0
    this.waiting = false

    this.pending.forEach(({ transaction, docRef, data, txCb }, i) => {
      let r = !this.waiting && this.onExecute(transaction, docRef, data, txCb)
      if (r) this.pending[i] = { executed: true }
    })

    this.pending = this.pending.filter(p => !p.executed)
    if (this.commited && this.pending.length <= this.writeLimit) {
      this.commit(this.onFinish, true)
    }
    return this
  }

  this.onExecute = (transaction, docRef, data, txCb) => {
    txCb = txCb || onTransaction
    if (this.waiting) {
      this.pending.push({ transaction, docRef, data, txCb, executed: false })
      return false
    }
    if (this.partialCounter >= this.writeLimit) {
      this.partialCounter = 0
      this.pending.push({ transaction, docRef, data, txCb, executed: false })
      this.timer()
      return false
    }
    switch (transaction) {
      case 'delete':
        refBatch.delete(docRef)
        break
      case 'update':
        refBatch.update(docRef, data)
        break
      case 'set':
        refBatch.set(docRef, data)
        break
      default:
        break
    }
    this.counter++
    this.totalCounter++
    this.partialCounter++
    this.transactions[transaction]++
    this.transactions.all.push({
      transaction,
      docRef,
      data: data || null,
      txCb,
      index: this.transactions.commits,
    })
    txCb &&
      this.stack.push(() => {
        txCb(data)
      })

    if (this.counter >= this.transactionLimit) {
      this.partialCommit()
    }
    return true
  }

  this.handleCommitError = (err, commitIndex, onHandlerFinish) => {
    let canFix = false
    let fixMsg = { msg: 'An error occurred. Trying to fix...', code: err.code }
    let commitTransactions = []
    let knownErrors = [
      'decrease transaction size',
      'transaction too big',
      'payload size exceeds',
      'transaction or write too big',
    ]
    switch (err.code) {
      case 3:
      case 'invalid-argument':
        if (err.toString && knownErrors.find(e => err.toString().toLowerCase().indexOf(e) > -1)) {
          canFix = this.transactionLimit > 0 && intents < 10
          if (canFix) {
            commitTransactions = this.transactions.all.filter(t => {
              return t.index === commitIndex
            })
            let reducedLimit = Math.floor(this.transactionLimit / 2)
            let tempBatch = new FirestoreBatchHandler(
              workRef,
              {
                ...options,
                waiting: this.waiting,
                partialCounter: this.partialCounter,
                limit: reducedLimit,
              },
              intents + 1
            )

            let txCount = {}
            commitTransactions.forEach(t => {
              txCount[t.transaction] = (txCount[t.transaction] || 0) + 1
            })

            this.log({
              ...fixMsg,
              msg: 'Trying to fix by decreasing payload size by half',
              handler: `Using error handler at commit #${commitIndex + 1}`,
              limit: reducedLimit,
              intents: intents + 1,
              transactions: txCount,
            })

            commitTransactions.forEach(t => {
              let { transaction, docRef, data } = t
              tempBatch[transaction](docRef, data)
            })

            tempBatch.commit(onHandlerFinish)
            this.handlerBatches.push(tempBatch)
          }
        }
        break
      default:
        break
    }
    if (!canFix) {
      if (err.toString && err.toString()) {
        this.log('err: toString >' + err.toString())
      } else this.log(err)
    }
    return this
  }

  this.onCommit = (isFinalCommit, onFinish) => {
    // console.log("--onCommit",this.counter,this.totalCounter);
    let stackLen = this.stack.length
    this.stack.forEach((cb, i) => cb())
    /* In case any of the callbacks incremented the stack */
    if (this.stack.length > stackLen) {
      for (let i = stackLen; i < this.stack.length; i++) {
        this.stack[i]()
      }
    }
    this.stack = []
    /* In case any callbacks added new transactions */
    if (isFinalCommit) {
      this.log(`${intents > 0 ? 'error handler' : 'final'} commit executed with ${this.totalCounter} transactions`)

      onFinish && onFinish(this.transactions, this.totalCounter)
      // this.log({ transactions: this.transactions })
      // this.reset();
    }
    return this
  }

  this.partialCommit = () => {
    // console.log("--partialCommit",this.counter,this.totalCounter);
    let commitIndex = this.transactions.commits

    this.counter = 0
    this.transactions.commits++

    refBatch
      .commit()
      .then(() => {
        return this.onCommit()
      })
      .catch(err => {
        this.handleCommitError(err, commitIndex, handlerTx => {
          this.logTx(handlerTx, { partial: true })
          this.onCommit()
        })
      })
    refBatch = workRef.batch()
    this.log(`${intents > 0 ? 'error handler' : ''} partial batch commited at ${this.totalCounter} transactions`)
    return this
  }

  this.commit = (onFinish, innerCommit) => {
    console.log('--commit', this.counter, this.totalCounter, this.pending.length, this.handlerBatches.length)
    this.commited = true

    let commitIndex = this.transactions.commits
    let pendingBatches = this.handlerBatches.filter(b => b.waiting)

    let end = () => {
      let isFinalCommit = true
      this.onCommit(isFinalCommit, onFinish)
      return true
    }

    if (this.waiting) {
      this.log(`--waiting: can't commit yet at ${this.totalCounter} transactions`)
      this.onFinish = end
      return false
    }

    if (pendingBatches.length) {
      this.log(`--waiting: can't commit yet at ${this.totalCounter} transactions: ${pendingBatches.length} error handlers still active`)
      this.onFinish = end
      return false
    }

    if (!this.counter) {
      return end()
    }

    this.counter = 0
    this.transactions.commits++

    refBatch
      .commit()
      .then(() => {
        return end()
      })
      .catch(err => {
        this.handleCommitError(err, commitIndex, handlerTx => {
          this.logTx(handlerTx, { final: true })
          return end()
        })
      })
    refBatch = workRef.batch()

    return true
  }

  this.reset = (keys, hard) => {
    // console.log("--RESET")
    if (this.waiting) {
      this.stack.push(() => {
        this.reset(keys, hard)
      })
      return this
    }
    keys = keys || Object.keys(transactions)
    hard = hard === true
    Object.keys(transactions).forEach(key => {
      if (keys.includes(key)) {
        this.transactions[key] = JSON.parse(JSON.stringify(transactions[key]))
      }
    })
    if (hard) {
      this.counter = 0
      this.totalCounter = 0
      this.stack = []
      refBatch = workRef.batch()
    }
    return this
  }

  this.init = () => {
    if (this.waiting) this.startTimer()
    return this
  }

  this.init()
}

// LOCAL STORAGE
export function DharmaStorage(targetStorage) {
  let self = this

  self.targetStorage = targetStorage || 'localStorage'
  self.manager = targetStorage === 'localStorage' ? window.localStorage : window.sessionStorage

  self.create = function (key, obj) {
    if (!obj && !self.get(key)) {
      self.manager.setItem(key, '{}')
    } else if (obj && !obj[key]) {
      obj[key] = {}
    }
  }

  self.set = function (key, obj) {
    try {
      self.manager.setItem(key, JSON.stringify(obj))
    } catch (err) {
      self.log(err)
    }
  }

  self.get = function (key, defeaultValue) {
    let defaultVal = defeaultValue === undefined ? {} : defeaultValue
    if (self.manager.getItem(key)) {
      try {
        return JSON.parse(self.manager.getItem(key))
      } catch (e) {
        return defaultVal
      }
    } else {
      return defaultVal
    }
  }

  self.find = function (key, subkey, searchValue, match) {
    if (self.manager.getItem(key)) {
      try {
        var obj = self.get(key)

        if (isDefined(searchValue)) {
          if (Array.isArray(obj)) {
            for (var k = 0; k < obj.length; k++) {
              if (
                obj[k][subkey] == searchValue ||
                (match === false && typeof obj[k][subkey] === 'string' && hasStr(obj[k][subkey].toLowerCase(), searchValue.toLowerCase()))
              ) {
                return obj[k]
              }
            }
          } else if (!isEmptyObject(obj)) {
            for (var k in obj) {
              if (
                obj[k][subkey] == searchValue ||
                (match === false && typeof obj[k][subkey] === 'string' && hasStr(obj[k][subkey].toLowerCase(), searchValue.toLowerCase()))
              ) {
                return obj[k]
              }
            }
          } else {
          }
        } else {
          return obj[subkey] ? obj[subkey] : {}
        }

        return {}
      } catch (e) {
        return {}
      }
    } else {
      return {}
    }
  }

  self.push = function (key, subkey, val) {
    let obj = self.get(key)
    if (typeof obj !== 'object') {
      obj = {}
    }
    if (!(subkey in obj)) {
      obj[subkey] = []
    }

    obj[subkey].push(val)
    self.set(key, obj)
  }

  self.update = function (key, subkey, val) {
    let obj = self.get(key, true)
    if (obj) {
      obj[subkey] = val
      self.set(key, obj)
    } else {
      obj = {}
      obj[subkey] = val
      self.set(key, obj)
    }

    return obj
  }

  self.clone = function (obj) {
    return window.jsonClone(obj)
  }

  self.remove = function (key) {
    self.manager.removeItem(key)
  }

  self.delete = function (key, subkey) {
    try {
      var tempObj = self.get(key)
      delete tempObj[subkey]
      self.update(key, tempObj)
    } catch (err) {
      self.log(err)
    }
  }

  self.concat = function (str, obj) {
    if (typeof str === 'string' && !Array.isArray(obj) && typeof obj === 'object' && !isEmptyObject(obj)) {
      var arr = []
      for (var k in obj) arr.push(obj[k])
      return arr.join(str)
    } else {
      return ''
    }
  }

  self.log = function (logMsg, active) {
    try {
      if (isBrowser()) {
        console.log(logMsg)
      } else {
        if (typeof logMsg === 'object') {
          console.log(JSON.stringify(logMsg))
        } else {
          console.log(logMsg)
        }
      }
    } catch (err) {
      console.log(logMsg)
    }
  }
}

// OBJECT
// --------------------------
export function isEmptyObject(obj) {
  return Boolean(obj) && !Array.isArray(obj) && typeof obj === 'object' && !Object.keys(obj).length
}
export function isEmptyValuesObject(obj, acceptNull, acceptEmpty, acceptZero) {
  return (
    !isLoopable(obj) ||
    !Object.keys(obj).find(k =>
      isLoopable(obj[k]) ? !isEmptyValuesObject(obj[k], acceptNull, acceptEmpty, acceptZero) : isDefined(obj[k], acceptNull, acceptEmpty, acceptZero)
    )
  )
}
export function isLoopableObject(obj) {
  return Boolean(obj) && !Array.isArray(obj) && typeof obj === 'object' && Object.keys(obj) && Object.keys(obj).length
}
export function isLoopable(val) {
  return Array.isArray(val) || isLoopableObject(val)
}

// ------------------------------------------
// DRAGABBLE
// ------------------------------------------
let initialCoords = {},
  coordsTimeout
export const getDraggableStyle = (snapshot, { draggableProps, ...provided }, staticAxis) => {
  // console.log(' ------------- ');
  // console.log({snapshot, draggableProps });
  clearTimeout(coordsTimeout)
  coordsTimeout = setTimeout(() => {
    initialCoords = {}
  }, 5000)

  if (staticAxis === 'x' && ((draggableProps.style || {}).left || (draggableProps.style.transform || '').match(/translate\((.)([0-9]+)px/))) {
    const transform = (draggableProps.style.transform || '').replace(/translate\((.)([0-9]+)px/, 'translate(0px')
    let hasX = 'x' in initialCoords
    let x = hasX ? initialCoords.x : draggableProps.style.left || 0
    !hasX && (initialCoords = { x })
    // console.log('transform',{x, transform, initialCoords});
    return {
      ...draggableProps.style,
      left: x,
      transform,
      WebkitTransform: transform,
    }
  }

  if (
    staticAxis === 'y' &&
    ((draggableProps.style || {}).top || (draggableProps.style.transform || '').match(/translate\((.)([0-9]+)px,(.)(.)([0-9]+)px\)/))
  ) {
    const transform = (draggableProps.style.transform || '').replace(/translate\((.)([0-9]+)px,(.)(.)([0-9]+)px\)/, m =>
      (m || '').replace(/,(.)(.)([0-9]+)px\)/, ', 0px)')
    )
    let hasY = 'y' in initialCoords
    let y = hasY ? initialCoords.y : draggableProps.style.top || 0
    !hasY && (initialCoords = { y })
    // console.log('transform',{y, transform, initialCoords});
    return {
      ...draggableProps.style,
      top: y,
      transform,
      WebkitTransform: transform,
    }
  }

  return draggableProps.style
}

export function onDragReorder(result, srcItems, itemUpdateFn) {
  // dropped outside the list
  if (!result.destination) {
    return
  }

  // DRAG MAGIC STARTS HERE
  // --------------------------------------
  const { source, destination } = result

  const srcCol = source.droppableId
  const dstCol = destination.droppableId

  const srcIndex = source.index
  const dstIndex = destination.index

  let updatedItems = window.jsonClone(srcItems)

  if (srcCol === dstCol) {
    const reorder = (selected, startIndex, endIndex) => {
      const [removed] = selected.splice(startIndex, 1)
      let updatedItem = window.jsonClone(removed)
      if (typeof itemUpdateFn === 'function') {
        updatedItem = itemUpdateFn(updatedItem)
      }
      selected.splice(endIndex, 0, updatedItem)
      return selected
    }

    updatedItems = reorder(window.jsonClone(srcItems), srcIndex, dstIndex)
  }

  return updatedItems
}

export function getTriggersContents(trigger) {
  switch (trigger) {
    case APP_TRIGGERS.APP_TRIGGER_GEO_COUNTRY:
      return getCountries()
    default:
      break
  }
}
