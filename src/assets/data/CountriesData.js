export const countries = {
  'AF': {
    'id': 1,
    'code': 'AF',
    'countryCode': 'AF',
    'name': 'Afghanistan',
    'country': 'Afghanistan',
    'phoneCode': '93'
  },
  'AL': {
    'id': 2,
    'code': 'AL',
    'countryCode': 'AL',
    'name': 'Albania',
    'country': 'Albania',
    'phoneCode': '355'
  },
  'DZ': {
    'id': 3,
    'code': 'DZ',
    'countryCode': 'DZ',
    'name': 'Algeria',
    'country': 'Algeria',
    'phoneCode': '213'
  },
  'AS': {
    'id': 4,
    'code': 'AS',
    'countryCode': 'AS',
    'name': 'American Samoa',
    'country': 'American Samoa',
    'phoneCode': '1684'
  },
  'AD': {
    'id': 5,
    'code': 'AD',
    'countryCode': 'AD',
    'name': 'Andorra',
    'country': 'Andorra',
    'phoneCode': '376'
  },
  'AO': {
    'id': 6,
    'code': 'AO',
    'countryCode': 'AO',
    'name': 'Angola',
    'country': 'Angola',
    'phoneCode': '244'
  },
  'AI': {
    'id': 7,
    'code': 'AI',
    'countryCode': 'AI',
    'name': 'Anguilla',
    'country': 'Anguilla',
    'phoneCode': '1264'
  },
  'AQ': {
    'id': 8,
    'code': 'AQ',
    'countryCode': 'AQ',
    'name': 'Antarctica',
    'country': 'Antarctica',
    'phoneCode': '0'
  },
  'AG': {
    'id': 9,
    'code': 'AG',
    'countryCode': 'AG',
    'name': 'Antigua And Barbuda',
    'country': 'Antigua And Barbuda',
    'phoneCode': '1268'
  },
  'AR': {
    'id': 10,
    'code': 'AR',
    'countryCode': 'AR',
    'name': 'Argentina',
    'country': 'Argentina',
    'phoneCode': '54'
  },
  'AM': {
    'id': 11,
    'code': 'AM',
    'countryCode': 'AM',
    'name': 'Armenia',
    'country': 'Armenia',
    'phoneCode': '374'
  },
  'AW': {
    'id': 12,
    'code': 'AW',
    'countryCode': 'AW',
    'name': 'Aruba',
    'country': 'Aruba',
    'phoneCode': '297'
  },
  'AU': {
    'id': 13,
    'code': 'AU',
    'countryCode': 'AU',
    'name': 'Australia',
    'country': 'Australia',
    'phoneCode': '61'
  },
  'AT': {
    'id': 14,
    'code': 'AT',
    'countryCode': 'AT',
    'name': 'Austria',
    'country': 'Austria',
    'phoneCode': '43'
  },
  'AZ': {
    'id': 15,
    'code': 'AZ',
    'countryCode': 'AZ',
    'name': 'Azerbaijan',
    'country': 'Azerbaijan',
    'phoneCode': '994'
  },
  'BS': {
    'id': 16,
    'code': 'BS',
    'countryCode': 'BS',
    'name': 'Bahamas The',
    'country': 'Bahamas The',
    'phoneCode': '1242'
  },
  'BH': {
    'id': 17,
    'code': 'BH',
    'countryCode': 'BH',
    'name': 'Bahrain',
    'country': 'Bahrain',
    'phoneCode': '973'
  },
  'BD': {
    'id': 18,
    'code': 'BD',
    'countryCode': 'BD',
    'name': 'Bangladesh',
    'country': 'Bangladesh',
    'phoneCode': '880'
  },
  'BB': {
    'id': 19,
    'code': 'BB',
    'countryCode': 'BB',
    'name': 'Barbados',
    'country': 'Barbados',
    'phoneCode': '1246'
  },
  'BY': {
    'id': 20,
    'code': 'BY',
    'countryCode': 'BY',
    'name': 'Belarus',
    'country': 'Belarus',
    'phoneCode': '375'
  },
  'BE': {
    'id': 21,
    'code': 'BE',
    'countryCode': 'BE',
    'name': 'Belgium',
    'country': 'Belgium',
    'phoneCode': '32'
  },
  'BZ': {
    'id': 22,
    'code': 'BZ',
    'countryCode': 'BZ',
    'name': 'Belize',
    'country': 'Belize',
    'phoneCode': '501'
  },
  'BJ': {
    'id': 23,
    'code': 'BJ',
    'countryCode': 'BJ',
    'name': 'Benin',
    'country': 'Benin',
    'phoneCode': '229'
  },
  'BM': {
    'id': 24,
    'code': 'BM',
    'countryCode': 'BM',
    'name': 'Bermuda',
    'country': 'Bermuda',
    'phoneCode': '1441'
  },
  'BT': {
    'id': 25,
    'code': 'BT',
    'countryCode': 'BT',
    'name': 'Bhutan',
    'country': 'Bhutan',
    'phoneCode': '975'
  },
  'BO': {
    'id': 26,
    'code': 'BO',
    'countryCode': 'BO',
    'name': 'Bolivia',
    'country': 'Bolivia',
    'phoneCode': '591'
  },
  'BA': {
    'id': 27,
    'code': 'BA',
    'countryCode': 'BA',
    'name': 'Bosnia and Herzegovina',
    'country': 'Bosnia and Herzegovina',
    'phoneCode': '387'
  },
  'BW': {
    'id': 28,
    'code': 'BW',
    'countryCode': 'BW',
    'name': 'Botswana',
    'country': 'Botswana',
    'phoneCode': '267'
  },
  'BV': {
    'id': 29,
    'code': 'BV',
    'countryCode': 'BV',
    'name': 'Bouvet Island',
    'country': 'Bouvet Island',
    'phoneCode': '0'
  },
  'BR': {
    'id': 30,
    'code': 'BR',
    'countryCode': 'BR',
    'name': 'Brasil',
    'country': 'Brazil',
    'phoneCode': '55'
  },
  'IO': {
    'id': 31,
    'code': 'IO',
    'countryCode': 'IO',
    'name': 'British Indian Ocean Territory',
    'country': 'British Indian Ocean Territory',
    'phoneCode': '246'
  },
  'BN': {
    'id': 32,
    'code': 'BN',
    'countryCode': 'BN',
    'name': 'Brunei',
    'country': 'Brunei',
    'phoneCode': '673'
  },
  'BG': {
    'id': 33,
    'code': 'BG',
    'countryCode': 'BG',
    'name': 'Bulgaria',
    'country': 'Bulgaria',
    'phoneCode': '359'
  },
  'BF': {
    'id': 34,
    'code': 'BF',
    'countryCode': 'BF',
    'name': 'Burkina Faso',
    'country': 'Burkina Faso',
    'phoneCode': '226'
  },
  'BI': {
    'id': 35,
    'code': 'BI',
    'countryCode': 'BI',
    'name': 'Burundi',
    'country': 'Burundi',
    'phoneCode': '257'
  },
  'KH': {
    'id': 36,
    'code': 'KH',
    'countryCode': 'KH',
    'name': 'Cambodia',
    'country': 'Cambodia',
    'phoneCode': '855'
  },
  'CM': {
    'id': 37,
    'code': 'CM',
    'countryCode': 'CM',
    'name': 'Cameroon',
    'country': 'Cameroon',
    'phoneCode': '237'
  },
  'CA': {
    'id': 38,
    'code': 'CA',
    'countryCode': 'CA',
    'name': 'Canada',
    'country': 'Canada',
    'phoneCode': '1'
  },
  'CV': {
    'id': 39,
    'code': 'CV',
    'countryCode': 'CV',
    'name': 'Cape Verde',
    'country': 'Cape Verde',
    'phoneCode': '238'
  },
  'KY': {
    'id': 40,
    'code': 'KY',
    'countryCode': 'KY',
    'name': 'Cayman Islands',
    'country': 'Cayman Islands',
    'phoneCode': '1345'
  },
  'CF': {
    'id': 41,
    'code': 'CF',
    'countryCode': 'CF',
    'name': 'Central African Republic',
    'country': 'Central African Republic',
    'phoneCode': '236'
  },
  'TD': {
    'id': 42,
    'code': 'TD',
    'countryCode': 'TD',
    'name': 'Chad',
    'country': 'Chad',
    'phoneCode': '235'
  },
  'CL': {
    'id': 43,
    'code': 'CL',
    'countryCode': 'CL',
    'name': 'Chile',
    'country': 'Chile',
    'phoneCode': '56'
  },
  'CN': {
    'id': 44,
    'code': 'CN',
    'countryCode': 'CN',
    'name': 'China',
    'country': 'China',
    'phoneCode': '86'
  },
  'CX': {
    'id': 45,
    'code': 'CX',
    'countryCode': 'CX',
    'name': 'Christmas Island',
    'country': 'Christmas Island',
    'phoneCode': '61'
  },
  'CC': {
    'id': 46,
    'code': 'CC',
    'countryCode': 'CC',
    'name': 'Cocos (Keeling) Islands',
    'country': 'Cocos (Keeling) Islands',
    'phoneCode': '672'
  },
  'CO': {
    'id': 47,
    'code': 'CO',
    'countryCode': 'CO',
    'name': 'Colombia',
    'country': 'Colombia',
    'phoneCode': '57'
  },
  'KM': {
    'id': 48,
    'code': 'KM',
    'countryCode': 'KM',
    'name': 'Comoros',
    'country': 'Comoros',
    'phoneCode': '269'
  },
  'CG': {
    'id': 49,
    'code': 'CG',
    'countryCode': 'CG',
    'name': 'Republic Of The Congo',
    'country': 'Republic Of The Congo',
    'phoneCode': '242'
  },
  'CD': {
    'id': 50,
    'code': 'CD',
    'countryCode': 'CD',
    'name': 'Democratic Republic Of The Congo',
    'country': 'Democratic Republic Of The Congo',
    'phoneCode': '242'
  },
  'CK': {
    'id': 51,
    'code': 'CK',
    'countryCode': 'CK',
    'name': 'Cook Islands',
    'country': 'Cook Islands',
    'phoneCode': '682'
  },
  'CR': {
    'id': 52,
    'code': 'CR',
    'countryCode': 'CR',
    'name': 'Costa Rica',
    'country': 'Costa Rica',
    'phoneCode': '506'
  },
  'CI': {
    'id': 53,
    'code': 'CI',
    'countryCode': 'CI',
    'name': 'Cote D\'\'Ivoire (Ivory Coast)',
    'country': 'Cote D\'\'Ivoire (Ivory Coast)',
    'phoneCode': '225'
  },
  'HR': {
    'id': 54,
    'code': 'HR',
    'countryCode': 'HR',
    'name': 'Croatia (Hrvatska)',
    'country': 'Croatia (Hrvatska)',
    'phoneCode': '385'
  },
  'CU': {
    'id': 55,
    'code': 'CU',
    'countryCode': 'CU',
    'name': 'Cuba',
    'country': 'Cuba',
    'phoneCode': '53'
  },
  'CY': {
    'id': 56,
    'code': 'CY',
    'countryCode': 'CY',
    'name': 'Cyprus',
    'country': 'Cyprus',
    'phoneCode': '357'
  },
  'CZ': {
    'id': 57,
    'code': 'CZ',
    'countryCode': 'CZ',
    'name': 'Czech Republic',
    'country': 'Czech Republic',
    'phoneCode': '420'
  },
  'DK': {
    'id': 58,
    'code': 'DK',
    'countryCode': 'DK',
    'name': 'Denmark',
    'country': 'Denmark',
    'phoneCode': '45'
  },
  'DJ': {
    'id': 59,
    'code': 'DJ',
    'countryCode': 'DJ',
    'name': 'Djibouti',
    'country': 'Djibouti',
    'phoneCode': '253'
  },
  'DM': {
    'id': 60,
    'code': 'DM',
    'countryCode': 'DM',
    'name': 'Dominica',
    'country': 'Dominica',
    'phoneCode': '1767'
  },
  'DO': {
    'id': 61,
    'code': 'DO',
    'countryCode': 'DO',
    'name': 'Dominican Republic',
    'country': 'Dominican Republic',
    'phoneCode': '1809'
  },
  'TP': {
    'id': 62,
    'code': 'TP',
    'countryCode': 'TP',
    'name': 'East Timor',
    'country': 'East Timor',
    'phoneCode': '670'
  },
  'EC': {
    'id': 63,
    'code': 'EC',
    'countryCode': 'EC',
    'name': 'Ecuador',
    'country': 'Ecuador',
    'phoneCode': '593'
  },
  'EG': {
    'id': 64,
    'code': 'EG',
    'countryCode': 'EG',
    'name': 'Egypt',
    'country': 'Egypt',
    'phoneCode': '20'
  },
  'SV': {
    'id': 65,
    'code': 'SV',
    'countryCode': 'SV',
    'name': 'El Salvador',
    'country': 'El Salvador',
    'phoneCode': '503'
  },
  'GQ': {
    'id': 66,
    'code': 'GQ',
    'countryCode': 'GQ',
    'name': 'Equatorial Guinea',
    'country': 'Equatorial Guinea',
    'phoneCode': '240'
  },
  'ER': {
    'id': 67,
    'code': 'ER',
    'countryCode': 'ER',
    'name': 'Eritrea',
    'country': 'Eritrea',
    'phoneCode': '291'
  },
  'EE': {
    'id': 68,
    'code': 'EE',
    'countryCode': 'EE',
    'name': 'Estonia',
    'country': 'Estonia',
    'phoneCode': '372'
  },
  'ET': {
    'id': 69,
    'code': 'ET',
    'countryCode': 'ET',
    'name': 'Ethiopia',
    'country': 'Ethiopia',
    'phoneCode': '251'
  },
  'XA': {
    'id': 70,
    'code': 'XA',
    'countryCode': 'XA',
    'name': 'External Territories of Australia',
    'country': 'External Territories of Australia',
    'phoneCode': '61'
  },
  'FK': {
    'id': 71,
    'code': 'FK',
    'countryCode': 'FK',
    'name': 'Falkland Islands',
    'country': 'Falkland Islands',
    'phoneCode': '500'
  },
  'FO': {
    'id': 72,
    'code': 'FO',
    'countryCode': 'FO',
    'name': 'Faroe Islands',
    'country': 'Faroe Islands',
    'phoneCode': '298'
  },
  'FJ': {
    'id': 73,
    'code': 'FJ',
    'countryCode': 'FJ',
    'name': 'Fiji Islands',
    'country': 'Fiji Islands',
    'phoneCode': '679'
  },
  'FI': {
    'id': 74,
    'code': 'FI',
    'countryCode': 'FI',
    'name': 'Finland',
    'country': 'Finland',
    'phoneCode': '358'
  },
  'FR': {
    'id': 75,
    'code': 'FR',
    'countryCode': 'FR',
    'name': 'France',
    'country': 'France',
    'phoneCode': '33'
  },
  'GF': {
    'id': 76,
    'code': 'GF',
    'countryCode': 'GF',
    'name': 'French Guiana',
    'country': 'French Guiana',
    'phoneCode': '594'
  },
  'PF': {
    'id': 77,
    'code': 'PF',
    'countryCode': 'PF',
    'name': 'French Polynesia',
    'country': 'French Polynesia',
    'phoneCode': '689'
  },
  'TF': {
    'id': 78,
    'code': 'TF',
    'countryCode': 'TF',
    'name': 'French Southern Territories',
    'country': 'French Southern Territories',
    'phoneCode': '0'
  },
  'GA': {
    'id': 79,
    'code': 'GA',
    'countryCode': 'GA',
    'name': 'Gabon',
    'country': 'Gabon',
    'phoneCode': '241'
  },
  'GM': {
    'id': 80,
    'code': 'GM',
    'countryCode': 'GM',
    'name': 'Gambia The',
    'country': 'Gambia The',
    'phoneCode': '220'
  },
  'GE': {
    'id': 81,
    'code': 'GE',
    'countryCode': 'GE',
    'name': 'Georgia',
    'country': 'Georgia',
    'phoneCode': '995'
  },
  'DE': {
    'id': 82,
    'code': 'DE',
    'countryCode': 'DE',
    'name': 'Germany',
    'country': 'Germany',
    'phoneCode': '49'
  },
  'GH': {
    'id': 83,
    'code': 'GH',
    'countryCode': 'GH',
    'name': 'Ghana',
    'country': 'Ghana',
    'phoneCode': '233'
  },
  'GI': {
    'id': 84,
    'code': 'GI',
    'countryCode': 'GI',
    'name': 'Gibraltar',
    'country': 'Gibraltar',
    'phoneCode': '350'
  },
  'GR': {
    'id': 85,
    'code': 'GR',
    'countryCode': 'GR',
    'name': 'Greece',
    'country': 'Greece',
    'phoneCode': '30'
  },
  'GL': {
    'id': 86,
    'code': 'GL',
    'countryCode': 'GL',
    'name': 'Greenland',
    'country': 'Greenland',
    'phoneCode': '299'
  },
  'GD': {
    'id': 87,
    'code': 'GD',
    'countryCode': 'GD',
    'name': 'Grenada',
    'country': 'Grenada',
    'phoneCode': '1473'
  },
  'GP': {
    'id': 88,
    'code': 'GP',
    'countryCode': 'GP',
    'name': 'Guadeloupe',
    'country': 'Guadeloupe',
    'phoneCode': '590'
  },
  'GU': {
    'id': 89,
    'code': 'GU',
    'countryCode': 'GU',
    'name': 'Guam',
    'country': 'Guam',
    'phoneCode': '1671'
  },
  'GT': {
    'id': 90,
    'code': 'GT',
    'countryCode': 'GT',
    'name': 'Guatemala',
    'country': 'Guatemala',
    'phoneCode': '502'
  },
  'XU': {
    'id': 91,
    'code': 'XU',
    'countryCode': 'XU',
    'name': 'Guernsey and Alderney',
    'country': 'Guernsey and Alderney',
    'phoneCode': '44'
  },
  'GN': {
    'id': 92,
    'code': 'GN',
    'countryCode': 'GN',
    'name': 'Guinea',
    'country': 'Guinea',
    'phoneCode': '224'
  },
  'GW': {
    'id': 93,
    'code': 'GW',
    'countryCode': 'GW',
    'name': 'Guinea-Bissau',
    'country': 'Guinea-Bissau',
    'phoneCode': '245'
  },
  'GY': {
    'id': 94,
    'code': 'GY',
    'countryCode': 'GY',
    'name': 'Guyana',
    'country': 'Guyana',
    'phoneCode': '592'
  },
  'HT': {
    'id': 95,
    'code': 'HT',
    'countryCode': 'HT',
    'name': 'Haiti',
    'country': 'Haiti',
    'phoneCode': '509'
  },
  'HM': {
    'id': 96,
    'code': 'HM',
    'countryCode': 'HM',
    'name': 'Heard and McDonald Islands',
    'country': 'Heard and McDonald Islands',
    'phoneCode': '0'
  },
  'HN': {
    'id': 97,
    'code': 'HN',
    'countryCode': 'HN',
    'name': 'Honduras',
    'country': 'Honduras',
    'phoneCode': '504'
  },
  'HK': {
    'id': 98,
    'code': 'HK',
    'countryCode': 'HK',
    'name': 'Hong Kong S.A.R.',
    'country': 'Hong Kong S.A.R.',
    'phoneCode': '852'
  },
  'HU': {
    'id': 99,
    'code': 'HU',
    'countryCode': 'HU',
    'name': 'Hungary',
    'country': 'Hungary',
    'phoneCode': '36'
  },
  'IS': {
    'id': 100,
    'code': 'IS',
    'countryCode': 'IS',
    'name': 'Iceland',
    'country': 'Iceland',
    'phoneCode': '354'
  },
  'IN': {
    'id': 101,
    'code': 'IN',
    'countryCode': 'IN',
    'name': 'India',
    'country': 'India',
    'phoneCode': '91'
  },
  'ID': {
    'id': 102,
    'code': 'ID',
    'countryCode': 'ID',
    'name': 'Indonesia',
    'country': 'Indonesia',
    'phoneCode': '62'
  },
  'IR': {
    'id': 103,
    'code': 'IR',
    'countryCode': 'IR',
    'name': 'Iran',
    'country': 'Iran',
    'phoneCode': '98'
  },
  'IQ': {
    'id': 104,
    'code': 'IQ',
    'countryCode': 'IQ',
    'name': 'Iraq',
    'country': 'Iraq',
    'phoneCode': '964'
  },
  'IE': {
    'id': 105,
    'code': 'IE',
    'countryCode': 'IE',
    'name': 'Ireland',
    'country': 'Ireland',
    'phoneCode': '353'
  },
  'IL': {
    'id': 106,
    'code': 'IL',
    'countryCode': 'IL',
    'name': 'Israel',
    'country': 'Israel',
    'phoneCode': '972'
  },
  'IT': {
    'id': 107,
    'code': 'IT',
    'countryCode': 'IT',
    'name': 'Italy',
    'country': 'Italy',
    'phoneCode': '39'
  },
  'JM': {
    'id': 108,
    'code': 'JM',
    'countryCode': 'JM',
    'name': 'Jamaica',
    'country': 'Jamaica',
    'phoneCode': '1876'
  },
  'JP': {
    'id': 109,
    'code': 'JP',
    'countryCode': 'JP',
    'name': 'Japan',
    'country': 'Japan',
    'phoneCode': '81'
  },
  'XJ': {
    'id': 110,
    'code': 'XJ',
    'countryCode': 'XJ',
    'name': 'Jersey',
    'country': 'Jersey',
    'phoneCode': '44'
  },
  'JO': {
    'id': 111,
    'code': 'JO',
    'countryCode': 'JO',
    'name': 'Jordan',
    'country': 'Jordan',
    'phoneCode': '962'
  },
  'KZ': {
    'id': 112,
    'code': 'KZ',
    'countryCode': 'KZ',
    'name': 'Kazakhstan',
    'country': 'Kazakhstan',
    'phoneCode': '7'
  },
  'KE': {
    'id': 113,
    'code': 'KE',
    'countryCode': 'KE',
    'name': 'Kenya',
    'country': 'Kenya',
    'phoneCode': '254'
  },
  'KI': {
    'id': 114,
    'code': 'KI',
    'countryCode': 'KI',
    'name': 'Kiribati',
    'country': 'Kiribati',
    'phoneCode': '686'
  },
  'KP': {
    'id': 115,
    'code': 'KP',
    'countryCode': 'KP',
    'name': 'Korea North',
    'country': 'Korea North',
    'phoneCode': '850'
  },
  'KR': {
    'id': 116,
    'code': 'KR',
    'countryCode': 'KR',
    'name': 'Korea South',
    'country': 'Korea South',
    'phoneCode': '82'
  },
  'KW': {
    'id': 117,
    'code': 'KW',
    'countryCode': 'KW',
    'name': 'Kuwait',
    'country': 'Kuwait',
    'phoneCode': '965'
  },
  'KG': {
    'id': 118,
    'code': 'KG',
    'countryCode': 'KG',
    'name': 'Kyrgyzstan',
    'country': 'Kyrgyzstan',
    'phoneCode': '996'
  },
  'LA': {
    'id': 119,
    'code': 'LA',
    'countryCode': 'LA',
    'name': 'Laos',
    'country': 'Laos',
    'phoneCode': '856'
  },
  'LV': {
    'id': 120,
    'code': 'LV',
    'countryCode': 'LV',
    'name': 'Latvia',
    'country': 'Latvia',
    'phoneCode': '371'
  },
  'LB': {
    'id': 121,
    'code': 'LB',
    'countryCode': 'LB',
    'name': 'Lebanon',
    'country': 'Lebanon',
    'phoneCode': '961'
  },
  'LS': {
    'id': 122,
    'code': 'LS',
    'countryCode': 'LS',
    'name': 'Lesotho',
    'country': 'Lesotho',
    'phoneCode': '266'
  },
  'LR': {
    'id': 123,
    'code': 'LR',
    'countryCode': 'LR',
    'name': 'Liberia',
    'country': 'Liberia',
    'phoneCode': '231'
  },
  'LY': {
    'id': 124,
    'code': 'LY',
    'countryCode': 'LY',
    'name': 'Libya',
    'country': 'Libya',
    'phoneCode': '218'
  },
  'LI': {
    'id': 125,
    'code': 'LI',
    'countryCode': 'LI',
    'name': 'Liechtenstein',
    'country': 'Liechtenstein',
    'phoneCode': '423'
  },
  'LT': {
    'id': 126,
    'code': 'LT',
    'countryCode': 'LT',
    'name': 'Lithuania',
    'country': 'Lithuania',
    'phoneCode': '370'
  },
  'LU': {
    'id': 127,
    'code': 'LU',
    'countryCode': 'LU',
    'name': 'Luxembourg',
    'country': 'Luxembourg',
    'phoneCode': '352'
  },
  'MO': {
    'id': 128,
    'code': 'MO',
    'countryCode': 'MO',
    'name': 'Macau S.A.R.',
    'country': 'Macau S.A.R.',
    'phoneCode': '853'
  },
  'MK': {
    'id': 129,
    'code': 'MK',
    'countryCode': 'MK',
    'name': 'Macedonia',
    'country': 'Macedonia',
    'phoneCode': '389'
  },
  'MG': {
    'id': 130,
    'code': 'MG',
    'countryCode': 'MG',
    'name': 'Madagascar',
    'country': 'Madagascar',
    'phoneCode': '261'
  },
  'MW': {
    'id': 131,
    'code': 'MW',
    'countryCode': 'MW',
    'name': 'Malawi',
    'country': 'Malawi',
    'phoneCode': '265'
  },
  'MY': {
    'id': 132,
    'code': 'MY',
    'countryCode': 'MY',
    'name': 'Malaysia',
    'country': 'Malaysia',
    'phoneCode': '60'
  },
  'MV': {
    'id': 133,
    'code': 'MV',
    'countryCode': 'MV',
    'name': 'Maldives',
    'country': 'Maldives',
    'phoneCode': '960'
  },
  'ML': {
    'id': 134,
    'code': 'ML',
    'countryCode': 'ML',
    'name': 'Mali',
    'country': 'Mali',
    'phoneCode': '223'
  },
  'MT': {
    'id': 135,
    'code': 'MT',
    'countryCode': 'MT',
    'name': 'Malta',
    'country': 'Malta',
    'phoneCode': '356'
  },
  'XM': {
    'id': 136,
    'code': 'XM',
    'countryCode': 'XM',
    'name': 'Man (Isle of)',
    'country': 'Man (Isle of)',
    'phoneCode': '44'
  },
  'MH': {
    'id': 137,
    'code': 'MH',
    'countryCode': 'MH',
    'name': 'Marshall Islands',
    'country': 'Marshall Islands',
    'phoneCode': '692'
  },
  'MQ': {
    'id': 138,
    'code': 'MQ',
    'countryCode': 'MQ',
    'name': 'Martinique',
    'country': 'Martinique',
    'phoneCode': '596'
  },
  'MR': {
    'id': 139,
    'code': 'MR',
    'countryCode': 'MR',
    'name': 'Mauritania',
    'country': 'Mauritania',
    'phoneCode': '222'
  },
  'MU': {
    'id': 140,
    'code': 'MU',
    'countryCode': 'MU',
    'name': 'Mauritius',
    'country': 'Mauritius',
    'phoneCode': '230'
  },
  'YT': {
    'id': 141,
    'code': 'YT',
    'countryCode': 'YT',
    'name': 'Mayotte',
    'country': 'Mayotte',
    'phoneCode': '269'
  },
  'MX': {
    'id': 142,
    'code': 'MX',
    'countryCode': 'MX',
    'name': 'Mexico',
    'country': 'Mexico',
    'phoneCode': '52'
  },
  'FM': {
    'id': 143,
    'code': 'FM',
    'countryCode': 'FM',
    'name': 'Micronesia',
    'country': 'Micronesia',
    'phoneCode': '691'
  },
  'MD': {
    'id': 144,
    'code': 'MD',
    'countryCode': 'MD',
    'name': 'Moldova',
    'country': 'Moldova',
    'phoneCode': '373'
  },
  'MC': {
    'id': 145,
    'code': 'MC',
    'countryCode': 'MC',
    'name': 'Monaco',
    'country': 'Monaco',
    'phoneCode': '377'
  },
  'MN': {
    'id': 146,
    'code': 'MN',
    'countryCode': 'MN',
    'name': 'Mongolia',
    'country': 'Mongolia',
    'phoneCode': '976'
  },
  'MS': {
    'id': 147,
    'code': 'MS',
    'countryCode': 'MS',
    'name': 'Montserrat',
    'country': 'Montserrat',
    'phoneCode': '1664'
  },
  'MA': {
    'id': 148,
    'code': 'MA',
    'countryCode': 'MA',
    'name': 'Morocco',
    'country': 'Morocco',
    'phoneCode': '212'
  },
  'MZ': {
    'id': 149,
    'code': 'MZ',
    'countryCode': 'MZ',
    'name': 'Mozambique',
    'country': 'Mozambique',
    'phoneCode': '258'
  },
  'MM': {
    'id': 150,
    'code': 'MM',
    'countryCode': 'MM',
    'name': 'Myanmar',
    'country': 'Myanmar',
    'phoneCode': '95'
  },
  'NA': {
    'id': 151,
    'code': 'NA',
    'countryCode': 'NA',
    'name': 'Namibia',
    'country': 'Namibia',
    'phoneCode': '264'
  },
  'NR': {
    'id': 152,
    'code': 'NR',
    'countryCode': 'NR',
    'name': 'Nauru',
    'country': 'Nauru',
    'phoneCode': '674'
  },
  'NP': {
    'id': 153,
    'code': 'NP',
    'countryCode': 'NP',
    'name': 'Nepal',
    'country': 'Nepal',
    'phoneCode': '977'
  },
  'AN': {
    'id': 154,
    'code': 'AN',
    'countryCode': 'AN',
    'name': 'Netherlands Antilles',
    'country': 'Netherlands Antilles',
    'phoneCode': '599'
  },
  'NL': {
    'id': 155,
    'code': 'NL',
    'countryCode': 'NL',
    'name': 'Netherlands The',
    'country': 'Netherlands The',
    'phoneCode': '31'
  },
  'NC': {
    'id': 156,
    'code': 'NC',
    'countryCode': 'NC',
    'name': 'New Caledonia',
    'country': 'New Caledonia',
    'phoneCode': '687'
  },
  'NZ': {
    'id': 157,
    'code': 'NZ',
    'countryCode': 'NZ',
    'name': 'New Zealand',
    'country': 'New Zealand',
    'phoneCode': '64'
  },
  'NI': {
    'id': 158,
    'code': 'NI',
    'countryCode': 'NI',
    'name': 'Nicaragua',
    'country': 'Nicaragua',
    'phoneCode': '505'
  },
  'NE': {
    'id': 159,
    'code': 'NE',
    'countryCode': 'NE',
    'name': 'Niger',
    'country': 'Niger',
    'phoneCode': '227'
  },
  'NG': {
    'id': 160,
    'code': 'NG',
    'countryCode': 'NG',
    'name': 'Nigeria',
    'country': 'Nigeria',
    'phoneCode': '234'
  },
  'NU': {
    'id': 161,
    'code': 'NU',
    'countryCode': 'NU',
    'name': 'Niue',
    'country': 'Niue',
    'phoneCode': '683'
  },
  'NF': {
    'id': 162,
    'code': 'NF',
    'countryCode': 'NF',
    'name': 'Norfolk Island',
    'country': 'Norfolk Island',
    'phoneCode': '672'
  },
  'MP': {
    'id': 163,
    'code': 'MP',
    'countryCode': 'MP',
    'name': 'Northern Mariana Islands',
    'country': 'Northern Mariana Islands',
    'phoneCode': '1670'
  },
  'NO': {
    'id': 164,
    'code': 'NO',
    'countryCode': 'NO',
    'name': 'Norway',
    'country': 'Norway',
    'phoneCode': '47'
  },
  'OM': {
    'id': 165,
    'code': 'OM',
    'countryCode': 'OM',
    'name': 'Oman',
    'country': 'Oman',
    'phoneCode': '968'
  },
  'PK': {
    'id': 166,
    'code': 'PK',
    'countryCode': 'PK',
    'name': 'Pakistan',
    'country': 'Pakistan',
    'phoneCode': '92'
  },
  'PW': {
    'id': 167,
    'code': 'PW',
    'countryCode': 'PW',
    'name': 'Palau',
    'country': 'Palau',
    'phoneCode': '680'
  },
  'PS': {
    'id': 168,
    'code': 'PS',
    'countryCode': 'PS',
    'name': 'Palestinian Territory Occupied',
    'country': 'Palestinian Territory Occupied',
    'phoneCode': '970'
  },
  'PA': {
    'id': 169,
    'code': 'PA',
    'countryCode': 'PA',
    'name': 'Panama',
    'country': 'Panama',
    'phoneCode': '507'
  },
  'PG': {
    'id': 170,
    'code': 'PG',
    'countryCode': 'PG',
    'name': 'Papua new Guinea',
    'country': 'Papua new Guinea',
    'phoneCode': '675'
  },
  'PY': {
    'id': 171,
    'code': 'PY',
    'countryCode': 'PY',
    'name': 'Paraguay',
    'country': 'Paraguay',
    'phoneCode': '595'
  },
  'PE': {
    'id': 172,
    'code': 'PE',
    'countryCode': 'PE',
    'name': 'Peru',
    'country': 'Peru',
    'phoneCode': '51'
  },
  'PH': {
    'id': 173,
    'code': 'PH',
    'countryCode': 'PH',
    'name': 'Philippines',
    'country': 'Philippines',
    'phoneCode': '63'
  },
  'PN': {
    'id': 174,
    'code': 'PN',
    'countryCode': 'PN',
    'name': 'Pitcairn Island',
    'country': 'Pitcairn Island',
    'phoneCode': '0'
  },
  'PL': {
    'id': 175,
    'code': 'PL',
    'countryCode': 'PL',
    'name': 'Poland',
    'country': 'Poland',
    'phoneCode': '48'
  },
  'PT': {
    'id': 176,
    'code': 'PT',
    'countryCode': 'PT',
    'name': 'Portugal',
    'country': 'Portugal',
    'phoneCode': '351'
  },
  'PR': {
    'id': 177,
    'code': 'PR',
    'countryCode': 'PR',
    'name': 'Puerto Rico',
    'country': 'Puerto Rico',
    'phoneCode': '1787'
  },
  'QA': {
    'id': 178,
    'code': 'QA',
    'countryCode': 'QA',
    'name': 'Qatar',
    'country': 'Qatar',
    'phoneCode': '974'
  },
  'RE': {
    'id': 179,
    'code': 'RE',
    'countryCode': 'RE',
    'name': 'Reunion',
    'country': 'Reunion',
    'phoneCode': '262'
  },
  'RO': {
    'id': 180,
    'code': 'RO',
    'countryCode': 'RO',
    'name': 'Romania',
    'country': 'Romania',
    'phoneCode': '40'
  },
  'RU': {
    'id': 181,
    'code': 'RU',
    'countryCode': 'RU',
    'name': 'Russia',
    'country': 'Russia',
    'phoneCode': '70'
  },
  'RW': {
    'id': 182,
    'code': 'RW',
    'countryCode': 'RW',
    'name': 'Rwanda',
    'country': 'Rwanda',
    'phoneCode': '250'
  },
  'SH': {
    'id': 183,
    'code': 'SH',
    'countryCode': 'SH',
    'name': 'Saint Helena',
    'country': 'Saint Helena',
    'phoneCode': '290'
  },
  'KN': {
    'id': 184,
    'code': 'KN',
    'countryCode': 'KN',
    'name': 'Saint Kitts And Nevis',
    'country': 'Saint Kitts And Nevis',
    'phoneCode': '1869'
  },
  'LC': {
    'id': 185,
    'code': 'LC',
    'countryCode': 'LC',
    'name': 'Saint Lucia',
    'country': 'Saint Lucia',
    'phoneCode': '1758'
  },
  'PM': {
    'id': 186,
    'code': 'PM',
    'countryCode': 'PM',
    'name': 'Saint Pierre and Miquelon',
    'country': 'Saint Pierre and Miquelon',
    'phoneCode': '508'
  },
  'VC': {
    'id': 187,
    'code': 'VC',
    'countryCode': 'VC',
    'name': 'Saint Vincent And The Grenadines',
    'country': 'Saint Vincent And The Grenadines',
    'phoneCode': '1784'
  },
  'WS': {
    'id': 188,
    'code': 'WS',
    'countryCode': 'WS',
    'name': 'Samoa',
    'country': 'Samoa',
    'phoneCode': '684'
  },
  'SM': {
    'id': 189,
    'code': 'SM',
    'countryCode': 'SM',
    'name': 'San Marino',
    'country': 'San Marino',
    'phoneCode': '378'
  },
  'ST': {
    'id': 190,
    'code': 'ST',
    'countryCode': 'ST',
    'name': 'Sao Tome and Principe',
    'country': 'Sao Tome and Principe',
    'phoneCode': '239'
  },
  'SA': {
    'id': 191,
    'code': 'SA',
    'countryCode': 'SA',
    'name': 'Saudi Arabia',
    'country': 'Saudi Arabia',
    'phoneCode': '966'
  },
  'SN': {
    'id': 192,
    'code': 'SN',
    'countryCode': 'SN',
    'name': 'Senegal',
    'country': 'Senegal',
    'phoneCode': '221'
  },
  'RS': {
    'id': 193,
    'code': 'RS',
    'countryCode': 'RS',
    'name': 'Serbia',
    'country': 'Serbia',
    'phoneCode': '381'
  },
  'SC': {
    'id': 194,
    'code': 'SC',
    'countryCode': 'SC',
    'name': 'Seychelles',
    'country': 'Seychelles',
    'phoneCode': '248'
  },
  'SL': {
    'id': 195,
    'code': 'SL',
    'countryCode': 'SL',
    'name': 'Sierra Leone',
    'country': 'Sierra Leone',
    'phoneCode': '232'
  },
  'SG': {
    'id': 196,
    'code': 'SG',
    'countryCode': 'SG',
    'name': 'Singapore',
    'country': 'Singapore',
    'phoneCode': '65'
  },
  'SK': {
    'id': 197,
    'code': 'SK',
    'countryCode': 'SK',
    'name': 'Slovakia',
    'country': 'Slovakia',
    'phoneCode': '421'
  },
  'SI': {
    'id': 198,
    'code': 'SI',
    'countryCode': 'SI',
    'name': 'Slovenia',
    'country': 'Slovenia',
    'phoneCode': '386'
  },
  'XG': {
    'id': 199,
    'code': 'XG',
    'countryCode': 'XG',
    'name': 'Smaller Territories of the UK',
    'country': 'Smaller Territories of the UK',
    'phoneCode': '44'
  },
  'SB': {
    'id': 200,
    'code': 'SB',
    'countryCode': 'SB',
    'name': 'Solomon Islands',
    'country': 'Solomon Islands',
    'phoneCode': '677'
  },
  'SO': {
    'id': 201,
    'code': 'SO',
    'countryCode': 'SO',
    'name': 'Somalia',
    'country': 'Somalia',
    'phoneCode': '252'
  },
  'ZA': {
    'id': 202,
    'code': 'ZA',
    'countryCode': 'ZA',
    'name': 'South Africa',
    'country': 'South Africa',
    'phoneCode': '27'
  },
  'GS': {
    'id': 203,
    'code': 'GS',
    'countryCode': 'GS',
    'name': 'South Georgia',
    'country': 'South Georgia',
    'phoneCode': '0'
  },
  'SS': {
    'id': 204,
    'code': 'SS',
    'countryCode': 'SS',
    'name': 'South Sudan',
    'country': 'South Sudan',
    'phoneCode': '211'
  },
  'ES': {
    'id': 205,
    'code': 'ES',
    'countryCode': 'ES',
    'name': 'Spain',
    'country': 'Spain',
    'phoneCode': '34'
  },
  'LK': {
    'id': 206,
    'code': 'LK',
    'countryCode': 'LK',
    'name': 'Sri Lanka',
    'country': 'Sri Lanka',
    'phoneCode': '94'
  },
  'SD': {
    'id': 207,
    'code': 'SD',
    'countryCode': 'SD',
    'name': 'Sudan',
    'country': 'Sudan',
    'phoneCode': '249'
  },
  'SR': {
    'id': 208,
    'code': 'SR',
    'countryCode': 'SR',
    'name': 'Suriname',
    'country': 'Suriname',
    'phoneCode': '597'
  },
  'SJ': {
    'id': 209,
    'code': 'SJ',
    'countryCode': 'SJ',
    'name': 'Svalbard And Jan Mayen Islands',
    'country': 'Svalbard And Jan Mayen Islands',
    'phoneCode': '47'
  },
  'SZ': {
    'id': 210,
    'code': 'SZ',
    'countryCode': 'SZ',
    'name': 'Swaziland',
    'country': 'Swaziland',
    'phoneCode': '268'
  },
  'SE': {
    'id': 211,
    'code': 'SE',
    'countryCode': 'SE',
    'name': 'Sweden',
    'country': 'Sweden',
    'phoneCode': '46'
  },
  'CH': {
    'id': 212,
    'code': 'CH',
    'countryCode': 'CH',
    'name': 'Switzerland',
    'country': 'Switzerland',
    'phoneCode': '41'
  },
  'SY': {
    'id': 213,
    'code': 'SY',
    'countryCode': 'SY',
    'name': 'Syria',
    'country': 'Syria',
    'phoneCode': '963'
  },
  'TW': {
    'id': 214,
    'code': 'TW',
    'countryCode': 'TW',
    'name': 'Taiwan',
    'country': 'Taiwan',
    'phoneCode': '886'
  },
  'TJ': {
    'id': 215,
    'code': 'TJ',
    'countryCode': 'TJ',
    'name': 'Tajikistan',
    'country': 'Tajikistan',
    'phoneCode': '992'
  },
  'TZ': {
    'id': 216,
    'code': 'TZ',
    'countryCode': 'TZ',
    'name': 'Tanzania',
    'country': 'Tanzania',
    'phoneCode': '255'
  },
  'TH': {
    'id': 217,
    'code': 'TH',
    'countryCode': 'TH',
    'name': 'Thailand',
    'country': 'Thailand',
    'phoneCode': '66'
  },
  'TG': {
    'id': 218,
    'code': 'TG',
    'countryCode': 'TG',
    'name': 'Togo',
    'country': 'Togo',
    'phoneCode': '228'
  },
  'TK': {
    'id': 219,
    'code': 'TK',
    'countryCode': 'TK',
    'name': 'Tokelau',
    'country': 'Tokelau',
    'phoneCode': '690'
  },
  'TO': {
    'id': 220,
    'code': 'TO',
    'countryCode': 'TO',
    'name': 'Tonga',
    'country': 'Tonga',
    'phoneCode': '676'
  },
  'TT': {
    'id': 221,
    'code': 'TT',
    'countryCode': 'TT',
    'name': 'Trinidad And Tobago',
    'country': 'Trinidad And Tobago',
    'phoneCode': '1868'
  },
  'TN': {
    'id': 222,
    'code': 'TN',
    'countryCode': 'TN',
    'name': 'Tunisia',
    'country': 'Tunisia',
    'phoneCode': '216'
  },
  'TR': {
    'id': 223,
    'code': 'TR',
    'countryCode': 'TR',
    'name': 'Turkey',
    'country': 'Turkey',
    'phoneCode': '90'
  },
  'TM': {
    'id': 224,
    'code': 'TM',
    'countryCode': 'TM',
    'name': 'Turkmenistan',
    'country': 'Turkmenistan',
    'phoneCode': '7370'
  },
  'TC': {
    'id': 225,
    'code': 'TC',
    'countryCode': 'TC',
    'name': 'Turks And Caicos Islands',
    'country': 'Turks And Caicos Islands',
    'phoneCode': '1649'
  },
  'TV': {
    'id': 226,
    'code': 'TV',
    'countryCode': 'TV',
    'name': 'Tuvalu',
    'country': 'Tuvalu',
    'phoneCode': '688'
  },
  'UG': {
    'id': 227,
    'code': 'UG',
    'countryCode': 'UG',
    'name': 'Uganda',
    'country': 'Uganda',
    'phoneCode': '256'
  },
  'UA': {
    'id': 228,
    'code': 'UA',
    'countryCode': 'UA',
    'name': 'Ukraine',
    'country': 'Ukraine',
    'phoneCode': '380'
  },
  'AE': {
    'id': 229,
    'code': 'AE',
    'countryCode': 'AE',
    'name': 'United Arab Emirates',
    'country': 'United Arab Emirates',
    'phoneCode': '971'
  },
  'GB': {
    'id': 230,
    'code': 'GB',
    'countryCode': 'GB',
    'name': 'Reino Unido',
    'country': 'United Kingdom',
    'phoneCode': '44'
  },
  'US': {
    'id': 231,
    'code': 'US',
    'countryCode': 'US',
    'name': 'Estados Unidos',
    'country': 'United States',
    'phoneCode': '1'
  },
  'UM': {
    'id': 232,
    'code': 'UM',
    'countryCode': 'UM',
    'name': 'United States Minor Outlying Islands',
    'country': 'United States Minor Outlying Islands',
    'phoneCode': '1'
  },
  'UY': {
    'id': 233,
    'code': 'UY',
    'countryCode': 'UY',
    'name': 'Uruguay',
    'country': 'Uruguay',
    'phoneCode': '598'
  },
  'UZ': {
    'id': 234,
    'code': 'UZ',
    'countryCode': 'UZ',
    'name': 'Uzbekistan',
    'country': 'Uzbekistan',
    'phoneCode': '998'
  },
  'VU': {
    'id': 235,
    'code': 'VU',
    'countryCode': 'VU',
    'name': 'Vanuatu',
    'country': 'Vanuatu',
    'phoneCode': '678'
  },
  'VA': {
    'id': 236,
    'code': 'VA',
    'countryCode': 'VA',
    'name': 'Vatican City State (Holy See)',
    'country': 'Vatican City State (Holy See)',
    'phoneCode': '39'
  },
  'VE': {
    'id': 237,
    'code': 'VE',
    'countryCode': 'VE',
    'name': 'Venezuela',
    'country': 'Venezuela',
    'phoneCode': '58'
  },
  'VN': {
    'id': 238,
    'code': 'VN',
    'countryCode': 'VN',
    'name': 'Vietnam',
    'country': 'Vietnam',
    'phoneCode': '84'
  },
  'VG': {
    'id': 239,
    'code': 'VG',
    'countryCode': 'VG',
    'name': 'Virgin Islands (British)',
    'country': 'Virgin Islands (British)',
    'phoneCode': '1284'
  },
  'VI': {
    'id': 240,
    'code': 'VI',
    'countryCode': 'VI',
    'name': 'Virgin Islands (US)',
    'country': 'Virgin Islands (US)',
    'phoneCode': '1340'
  },
  'WF': {
    'id': 241,
    'code': 'WF',
    'countryCode': 'WF',
    'name': 'Wallis And Futuna Islands',
    'country': 'Wallis And Futuna Islands',
    'phoneCode': '681'
  },
  'EH': {
    'id': 242,
    'code': 'EH',
    'countryCode': 'EH',
    'name': 'Western Sahara',
    'country': 'Western Sahara',
    'phoneCode': '212'
  },
  'YE': {
    'id': 243,
    'code': 'YE',
    'countryCode': 'YE',
    'name': 'Yemen',
    'country': 'Yemen',
    'phoneCode': '967'
  },
  'YU': {
    'id': 244,
    'code': 'YU',
    'countryCode': 'YU',
    'name': 'Yugoslavia',
    'country': 'Yugoslavia',
    'phoneCode': '38'
  },
  'ZM': {
    'id': 245,
    'code': 'ZM',
    'countryCode': 'ZM',
    'name': 'Zambia',
    'country': 'Zambia',
    'phoneCode': '260'
  },
  'ZW': {
    'id': 246,
    'code': 'ZW',
    'countryCode': 'ZW',
    'name': 'Zimbabwe',
    'country': 'Zimbabwe',
    'phoneCode': '26'
  }
}
