/*======= Mini Sidebar Style Here =====*/
$span-menu-distance: 68px;

.mini-sidebar {
   .side-title {
      display: none;
   }
   .app-container-wrapper {
      left: $mini-sidebar-width !important;
      z-index: 0;
   }
   .sidebar-user {
      max-width: $mini-sidebar-width;
      .sidebar-user-block {
         .user-info {
            display: none;
            opacity: 0;
            transition: all 0.3s ease-in-out;
         }
      }
   }
   .rct-sidebar {
      width: $mini-sidebar-width;
      -webkit-transition-property: top, bottom, width;
      transition-property: top, bottom, width;
      -webkit-transition-duration: .2s, .2s, .35s;
      transition-duration: .2s, .2s, .35s;
      -webkit-transition-timing-function: linear, linear, ease;
      transition-timing-function: linear, linear, ease;
      -webkit-overflow-scrolling: touch;
      .site-logo {
         height: 70px;
         .logo-normal {
            opacity: 0;
            // transform: translate3d(-25px, 0, 0)
         }
      }
      .rct-mainMenu {
         .side-title {
            display: none !important;
         }
         > li {
            transition: all 0.3s ease-in-out;
            &:not(.item-solo) {
               padding: 1rem 2rem !important;
            }
            &:not(.list-item) {
               a {
                  padding: 0 !important;
                  display: flex;
                  align-items: center;
               }
            }
            &.item-solo {
               padding: 0;
               display: flex;
               align-items: center;
               > a {
                  padding: 1rem 2rem !important;
                  transition: all 0.3s ease-in-out;
                  width: 100%;
                  display: flex;
                  align-items: center;
               }
            }
            .menu-icon {
               float: left;
               text-align: center;
            }
            > a {
               .menu-icon {
                  @extend .menu-icon;
               }
            }
            > a .menu {
               left: 35px;
            }
            .menu {
               left: $span-menu-distance;
            }
            &.list-item:after {
               opacity: 0;
            }
            .menu,
            > a .menu {
               opacity: 0;
               transition: all 0.4s linear 0s;
               position: absolute;
               // transform: translate3d(-25px, 0, 0)
               flex: 0;
               display: none;
            }
         }
         .sub-menu {
            .menu-icon {
               position: relative;
               top: -2px;
            }
            ul li.list-item {
               min-height: 48px;
            }
         }
      }
   }
   @media (min-width: 993px) {
      .rct-sidebar {
         &:hover {
            width: $sidebar-width;
            .logo-normal {
               opacity: 1;
               transform: translate3d(0, 0, 0);
            }         
            .rct-mainMenu {
               .side-title {
                  display: block !important;
               }
            }
            .rct-mainMenu,
            .sub-menu {
               ul.type-multi > li > a .menu,
               ul li.list-item span.menu {
                  flex: 1;
                  max-width: calc(100% - 40px);
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  display: inline-block;
                  position: relative;
                  // transform: translate(0, -50%);
                  left: 0;
                  text-align: left;
               }
               ul.type-multi {
                  > li {
                     transition: .15s;
                  }
               }
               > li {
                  &:not(.item-solo) {
                     padding: 1rem 1.5rem !important;
                  }
                  &.item-solo {
                     a {
                        padding: 1rem 1.5rem !important;
                        .menu {
                           left: $span-menu-distance;
                        }
                     }
                  }
                  .menu,
                  > a .menu,
                  a {
                     opacity: 1;
                     flex: 1;
                     max-width: calc(100% - 40px);
                     overflow: hidden;
                     text-overflow: ellipsis;
                     white-space: nowrap;
                     display: inline-block;
                     transform: scale(1) translateZ(0) translate3d(0, 0, 0);
                     min-height: 20px;
                     transition: .15s;
                  }
                  &.list-item:after {
                     opacity: 1;
                  }
               }
            }
            nav.app-navigation {
               li a {
                  justify-content: flex-start;
               }
            }
         }
         &:not(:hover) {
            .site-logo {
               .logo-mini img {
                  max-width: 35px;
               }
            }         
            .sidebar-collapse-btn{
               opacity: 0;
            }
            .menu-icon {
               margin-right: 0;
            }
            .sub-menu {
               ul li {
                  .menu-icon {
                     position: relative;
                     top: -7px;
                  }
                  span.menu {
                     display: block;
                     position: absolute;
                     top: 30px;
                     left: 5px;
                     font-size: 0.6rem !important;
                     right: 3px;
                     text-align: center;
                     line-height: 1;
                  }                  
                  &.list-item:after{
                     display: none;
                  }
                  &.item-active {
                     + div ul {
                        display: block;
                        span.menu {
                           left: 0;
                           top: 20px;
                           padding-left: 0 !important;
                        }
                     }
                  }
               }
            }
            .navigation > ul > li.item-active:not(.item-multi) {
               + div ul {
                  display: block;
                  > li span.menu {
                     left: 0;
                     top: 20px;
                     padding-left: 0 !important;
                  }
               }               
            }
            .navigation > ul > li:not(.item-active ) {
               .sub-menu {
                  ul.type-multi {
                     > li:not(.item-current):not(.item-active) {
                        overflow: hidden;
                        max-height: 0;
                        min-height: 0;
                        padding-top: 0;
                        padding-bottom: 0;           
                        transition: .15s;
                     }
                  }
                  ul ul {
                     display: none;
                  }
               }
            }
            .rct-mainMenu > li:not(.item-multi):not(.item-active) + .sub-menu{
               display: none;
            }
            nav.app-navigation {
               li a {
                  justify-content: center;
                  span {
                     display: none;
                     &.badge {
                        left: 35px;
                     }
                  }
               }
            }
         }
      }
   }
   @media (max-width: 992px) {
      .rct-sidebar {
         .site-logo {
            .logo-mini img {
               max-width: 35px;
            }
         }         
         .sidebar-collapse-btn{
            opacity: 0;
         }
         .rct-mainMenu {
            .sub-menu ul li,
            > li {
               padding: 1rem 2rem !important;
               transition: all 0.3s ease-in-out;
               display: flex;
               align-items: center;
               justify-content: center;
               a {
                  padding-left: 0em !important;
               }
               .menu-icon {
                  margin-right: 0;
               }
            }
            .sub-menu ul li {
               .menu-icon {
                  position: relative;
                  top: -7px;
               }
               span.menu {
                  display: block;
                  position: absolute;
                  top: 30px;
                  left: 5px;
                  font-size: 0.6rem !important;
                  right: 3px;
                  text-align: center;
                  line-height: 1;
               }
               &.list-item:after {
                  display: none;
               }
               &.list-item + div {
                  li {
                     padding: 0em !important;
                     > a {
                        text-align: center;
                        padding: 1em !important;
                        span.menu{
                           position: unset;
                           font-size: 0.8rem !important;
                           padding: 0 !important;
                        }
                     }
                  }
               }
            }
         }
      }
   }
   .sub-menu {
      > li {
         > a {
            position: relative;
            opacity: 0;
            transition: all 0.4s linear 0s;
            transform: translate3d(-25px, 0, 0)
         }
      }
   }
   .rct-header {
      left: $mini-sidebar-width;
   }
   &.mini-sidebar-hover{
      .sidebar-user {
         max-width: $sidebar-width;
         .sidebar-user-block {
            .user-info {
               max-width: 100%;
               overflow: hidden;
               text-overflow: ellipsis;
               display: block;
               white-space: nowrap;
               display: block;
               opacity: 1;
            }
         }
      }
      
   }
}