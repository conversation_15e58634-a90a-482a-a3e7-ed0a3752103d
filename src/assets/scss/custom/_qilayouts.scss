.projects-wrapper {
    position: relative;
    left: 0;
    transition: .5s ease-in-out;
    max-width: calc(100% - 0px);
    > .RctSectionLoader.loader-overlay {
        .MuiCircularProgress-root {
            position: fixed;
            top: 50%;
            margin-top: -20px;
        }
    }
    &.adv-search-open {
        position: relative; 
        left: 300px;
        max-width: calc(100% - 300px);
    }
}

.adv-search-bar-wrap {
    position: fixed;
    z-index: 100;
    top: 0;
    left: -300px;
    width: 300px;
    max-width: 300px;
    height: 100vh;
    max-height: 100vh;
    transition: .5s ease-in-out;
    &.sticky {
        left: -215px;
        .adv-search-bar-toggle {
            background-color: #404660 !important;
            position: absolute;
            left: 100%;
            top: 0;
            border-radius: 0 5px 5px 0;
            transition: .5s ease-in-out;
        }
        .adv-search-bar-pin {
            position: absolute;
            top: 4px;
            right: 4px;
            font-size: 10px;
        }
    }
    &.open {
        left: $mini-sidebar-width;
        .adv-search-bar-toggle {
            left: -50px;
        }
    }
    &.pos-right {
        left: auto !important;
        right: -300px;
        &.sticky {
            .adv-search-bar-toggle {
                left: auto;
                right: 100%;
                border-radius: 5px 0 0 5px;
            }
            .adv-search-bar-pin {
                display: none;
            }
        }
        &.open {
            right: 0;
            .adv-search-bar-toggle {
                right: -50px;
            }
        }
    }
    .rct-block{
        max-height: 100vh;
    }
}

.adv-search-bar {
    legend.MuiFormLabel-root {
        font-size: 14px;
        padding: 0 10px;
    }
    .MuiFormControl-root {
        min-width: 264px;
    }
    .MuiInputBase-inputMarginDense,
    .MuiInputLabel-filled:not(.Mui-focused) {
        max-width: 264px;
    }
    .repeater-field {
        .CurrencyInput-wrapper,
        .CurrencyInput-wrapper:hover .CurrencyInput-innerWrapper,
        .CurrencyInput-innerWrapper,
        .MuiFormControl-root {
            min-width: 224px;
            fieldset,
            textarea,
            input[type="password"],
            input[type="number"],
            input[type="text"],
            .CurrencyInput-label,
            .MuiInputAdornment-root .MuiTypography-root  {
                color: $white;
                border-color: $white;
            }
        }
    }
    .MuiFormControlLabel-label {
        font-size: 12px;
    }
    .MuiFormGroup-row[role="radiogroup"] {
        padding: 0 0.625rem;
        .MuiFormControlLabel-root {
            margin-bottom: 0;
        }
        .MuiRadio-root {
            transform: scale(0.7);
        }
    }
    &.bg-dark {
        h4 {
            color: #fff;
        }
        .MuiFilledInput-inputMarginDense {
            input, 
            .MuiTypography-root {
                color: #fff !important;
            }
        }
        .MuiRadio-root, 
        .MuiFormLabel-root,
        .MuiIconButton-label,
        .MuiAutocomplete-input,
        .MuiFormControlLabel-root,
        .MuiInputBase-inputMarginDense,
        .MuiInputLabel-filled:not(.Mui-focused) {
            color: #fff;
        }
        .MuiFormGroup-row[role="radiogroup"] {
            background: #404660;
            border-bottom: 1px solid #252938;
        }
        .SelectPlaceholderComponent-loader {
            background-color: #404660 !important;
            .loader-overlay {
                background-color: #404660 !important;
            }
        }
    }
    &.hasButton {
        padding-bottom: 60px;
        .adv-search-bar-btn-wrapper {
            position: absolute;
            bottom: 0;
            width: 100%;
            left: 0;
            .adv-search-bar-btn {
                i {
                    position: absolute;
                    right: 0;
                }
            }
        }
    }
}

body:not(.mini-sidebar) {
    .adv-search-bar-wrap:not(.pos-right) {
        left: -$adv-search-bar-width;
        &.sticky {
            left: calc($mini-sidebar-width - $adv-search-bar-width);
        }
        &.open {
            left: $sidebar-width;
            .adv-search-bar-toggle {
                left: -50px;
            }
        }
    }        
}

.status-wrapper{
    .status-info {
        position: relative;
    }
    .___NOPE____status-info + .status-info:before {
        content: '';
        width: 1px;
        top: -20px;
        bottom: -20px;
        background: #efefef;
        display: block;
        position: absolute;
        transform: translate(-50%, 0);
        right: 150%;
    }
}

.page-title {
    // margin: -1.075rem 0 1.075rem;
    // margin: -$grid-gutter-width -1.475rem $page-title-margin-botton;
    margin: 0;
    margin-bottom: $linear-progress-height;
    padding: .875rem;
    background: #fff;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    height: $page-title-height;
    .page-title-wrap {
        .page-title-h2 {
            text-transform: none;
        }
        input::selection,
        ::selection {
            background-color: #dadbe1;
            color: #333;
        }
        &.editable {
            .page-title-h2,
            input {
                font-family: "Heebo", sans-serif;
                font-size: 1.375rem;
                color: #464D69;
                font-weight: 400;
                min-height: 30px;
                padding: 0 10px;
            }
            .page-title-h2:hover::after {
                content: "\E61C";
                font-family: 'themify';
                font-style: normal;
                font-weight: normal;
                font-variant: normal;
                text-transform: none;
                line-height: 1;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                position: relative;
                right: -5px;
                top: -10px;
                font-size: 13px;
            }
        }
        .MuiButton-label {
            white-space: nowrap;
        }    
    }
    + .MuiLinearProgress-root{
        margin-top: -$linear-progress-height;
    }
    .middleComponents {
        .MuiInputBase-input[class*="makeStyles-input-"] {
            padding: 0;
            min-height: 40px;
        }
    }
}

.main-content {
    padding: $grid-gutter-width; //24px;
}

.ReactTable-overflow {
    .rt-tbody,
    .rt-table {
        overflow: visible;
    }
}

@media (min-width: 768px) {
    .page-title-wrap.editable .page-title-h2,
    .page-title-wrap.editable input {
        min-width: 320px;
    }
}

/* === deal-widget in detail screen === */
.rct-block-content {
    > .deal-widget {
        .MuiTabs-root {
            margin-top: -5px;
        }
    }
}

/* === user-profile === */
.user-profile-widget {    
    > .bg-gradient-secondary {
        margin: -0.9rem -0.9rem;
    }
    .user-avatar {
        margin-top: -3rem;
    }
    .MuiTabs-root {
        background: #f8f9fa;
        margin: 0 -$block-spacing;
        box-shadow: 0px 1px 2px rgba(0,0,0,0.3);
    }
}

/* === user-profile in modal === */
.modal {
    .user-profile-widget {
        .MuiTabs-root {
            margin: 0 -15px;
        }
    }
}

/* ======= MuiLinearProgress in modal ======= */
.modal {
    .modal-body {
        > .LinearProgress-wrap,
        > .MuiLinearProgress-root {
            margin: -20px -15px 20px;
        }
    }
}

/* ======= MuiLinearProgress in Rct Block Content ======= */
.rct-block {
    .rct-full-block,
    .rct-block-content {
        > .LinearProgress-wrap:last-child,
        > .MuiLinearProgress-root:last-child {
            position: absolute;
            bottom: 0;
            z-index: 1;
            left: 0;
            width: 100%;
        }
    }
}

/* ======= mini modal on mouseleave ======= */
.modal.show[role="dialog"]:not(.modal-pin) {
    opacity: 1;
    &.focusin,
    + .modal-backdrop {
        transition: .5s;
        transform: scale(1) translateY(0);
        transform-origin: bottom right;
    }
    &.focusin.focusout {
        opacity: 0.75;
        transform: scale(0.2) translateY(-50px);
        + .modal-backdrop {
            opacity: 0;
            transform: scale(0);
        }
    }
}

/* ======= email-preview ======= */
.email-preview {
    .content-wrapper {
        img {
            max-width: 100%;
        }
        .preview p {
            word-break: break-word;
            max-width: 100%;
        }
    }
}

/* ======= PostFields ======= */
.PostFields-wrapper{
    margin-left: -10px;
    margin-right: -10px;
    .PostFields-block {
        padding-left: 10px;
        padding-right: 10px;
    }
}

.projects-wrapper {
    .rct-block {
        .rct-block-thumbnail a {
            display: block;
            width: 100%;
            .thumbnail {
                height: 300px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-wrap: wrap;
                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    display: block;
                }
            }
        }
    }    
}

.plans-wrapper {
    .plan-wrapper.current {
        .rct-block {
            box-shadow: 0 0 3px rgba($color: #000000, $alpha: 0.4);
            background: #f6f6f6;
        }
        + .plan-wrapper {
            .rct-block {
                box-shadow: 0 0 5px rgba($color: #000000, $alpha: 0.5);
                transform: scale(1.02);
            }
        }
    }
}

