
/* 
AUTOMATION 
*/
.automation-builder {
    display: flex;
    // flex-wrap: wrap;
    .automation-builder-sidebar {
        min-width: 300px;
        max-width: 45%;
        margin: 0;
    }
    .automation-builder-canvas {
        flex: 1;
        max-width: calc(100% - 300px);
    }
    &.orientation-right {
        .automation-builder-sidebar {
            order: 2;
        }
    }
}
.automation-canvas {
    .qi-icon-button {
        border: 2px solid #666;
    }
    .stage-col {
        position: relative;
        z-index: 1;
    }
    .stage-wrapper {
        height: 100%;
    }
    .stage-wrapper,
    .draggable-wrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0.5rem;
        border: 2px dashed transparent;
        border-radius: 5px;
        min-height: 75px;
        min-width: 125px;
    }
    &.triggers-canvas {
        .stage-wrapper {
            background-color: #dcdde3;
            justify-content: center;
        }
        .draggable-item + .draggable-item:before,
        .draggable-item + .draggable-item:after {
            display: none;
        }
    }
    .draggable-wrapper {
        .draggable-conditions-toggler {
            position: absolute;
            z-index: 1;
            right: -2px;
            top: -2px;
            background: #464d69;
            padding: 2px 10px;
            color: #fff;
            font-size: 13px;
            border-radius: 0 5px 0 5px;
        }
        .draggable-connector {
            height: 25px;
            line-height: 25px;
            display: none;
            width: 25px;
            position: absolute;
            bottom: 100%;
            margin-bottom: 8px;
            margin-left: 1px;
            left: 50%;
            background: #666;
            color: #fff;
            border-radius: 50%;
            text-align: center;
            transform: translateX(-50%);
            &:before {
                content: '';
                height: 70px;
                display: block;
                width: 2px;
                position: absolute;
                bottom: -25px;
                margin-bottom: 0;
                left: 50%;
                background: #666;
                z-index: -1;
            }
            &:after {
                content: "\f078";
                font: normal normal normal 14px/1 FontAwesome;
                font-size: 10px;
                text-rendering: auto;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
            &.draggable-connector-goto {
                display: inline-flex;
                bottom: 15px;
                margin: 0;
                align-items: center;
                justify-content: center;
                background: red;
                font-size: 24px;
                &:before {
                    height: 10px;
                    bottom: 25px;
                    background: red;
                }
                &:after {
                    display: none;
                }
            }
        }
        + .draggable-wrapper > .draggable-connector {
            display: block;
        }      
        .draggable-connector-item.isDragging {
            .draggable-connector-goto:before {
                display: none;
            }
        }
    }
    .draggable-conditions > .draggable-connector,
    .draggable-triggers-wrapper > .draggable-connector {
        display: block;
        width: 35px;
        height: 35px;
        line-height: 36px;
        margin-bottom: 2px;
        font-size: 10px;
        text-transform: uppercase;
        font-weight: 600;
    }
    // .draggable-conditions > .draggable-connector {
    //     margin-bottom: 20px;
    // }
    .draggable-conditions > .draggable-connector:before,
    .draggable-triggers-wrapper > .draggable-connector:before {
        bottom: -20px;
    }
    .draggable-conditions > .draggable-connector:after,
    .draggable-triggers-wrapper > .draggable-connector:after {
        content: attr(data-label);
        font-family: sans-serif;
        font-weight: 400;
        text-transform: uppercase;
    }
    // .draggable-triggers-wrapper .draggable-conditions {
    //     border: 2px dashed;
    // }
    .draggable-wrapper.isDragging + .draggable-wrapper .draggable-connector:before,
    .draggable-wrapper.isDragging + .draggable-wrapper .draggable-connector:after {
        display: none;
    }
    .draggable-item + .draggable-item,
    .draggable-wrapper + .draggable-wrapper {
        margin-top: 40px;
    }
    .draggable-helper-btn {
        position: absolute;
        left: 50%;
        top: -30px;
        font-size: 15px;
        width: 40px;
        height: 40px;
        margin: -3px;
        transform: scale(.8) translate(-50%, -50%);
        opacity: 0;
        transition: all 0.15s;
        background: #383e54 !important;
        &:hover {
            opacity: 1;
        }
    }
    .draggable-triggers-wrapper > .draggable-wrapper > .draggable-helper-btn,
    .draggable-conditions > .draggable-connector + .draggable-wrapper .draggable-helper-btn {
        margin: -1px;
        transform: scale(.9) translate(-50%, -50%);
    }
    .draggable-conditions > .draggable-connector + .draggable-wrapper .draggable-helper-btn {
        top: -40px;
    }
    .draggable-triggers-wrapper:first-of-type,
    .draggable-conditions > .draggable-connector + .draggable-triggers-wrapper {
        margin-top: 40px;
    }
    .float-btn {
        position: absolute;
        opacity: 0;
        z-index: 1;
        .MuiIconButton-label {
            box-shadow: 1px 1px 3px 1px rgba(0,0,0,0.5);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            background: #fff;
        }
        &.float-delete-btn {
            top: 50%;
            left: 0px;
            transform: translate(-10px, -25px);
        }
        &.float-create-btn {
            top: 100%;
            left: 50%;
            transform: translate(-23px, -23px);
        }
    }
    .group-col:hover {
        .float-btn {
            opacity: 1;
        }
    }
    .relation-btn {
        position: relative;
        display: flex;
        justify-content: space-between;
        height: 30px;
        line-height: 30px;
        width: 30px;
        margin: 10px 0;
        background: #666;
        color: #fff;
        border-radius: 50%;
        font-size: 12px;
        font-weight: bold;
        text-align: center;
        cursor: pointer;
        transition: .15s ease-in;
        span {
            width: 100%;
            display: inline-block;
            text-align: center;
            border-radius: 20px;
            transition: .15s ease-in;
            overflow: hidden;
        }
        &:not(.readOnly):hover {
            width: 60px;
            border-radius: 20px;
            span {
                width: 50%;                
                &.active {
                    background-color: $primary;
                }
            }
        }
        &.readOnly span:not(.active),
        &:not(:hover) span:not(.active){
            width: 0%;
        }
        &:after,
        &:before {
            content: '';
            height: 10px;
            display: block;
            width: 2px;
            position: absolute;
            bottom: 100%;
            margin-bottom: 0;
            left: 50%;
            background: #666;   
        }
        &:after {
            top: 100%;
        }
    }
    .group-relation-btn {
        position: absolute;
        left: 50%;
        z-index: 0;
        top: -15px;
        transform: translate(-50%, -100%);
        background: #a5a7b2;
        margin: 0;
        span {
            width: 100%;                
            height: 100%;                
        }
        &:before, 
        &:after {
            height: 15px;
            width: 2px;
            top: 100%;
            right: 50%;
            left: auto;
            background: #a5a7b2;
        }        
        &:after {
            bottom: 100%;
            top: auto;
        }
        &:not(.readOnly):hover {
            width: 60px;
            height: 30px;
            span {
                width: 50%;                
                height: 100%;                
            }
        }
        &.readOnly span:not(.active),
        &:not(:hover) span:not(.active){
            width: 0%;
            height: 100%;
        }
    }
    .stage-col + .stage-col:before {
        content: '';
        background: rgba(153, 153, 153, 0.2);
        display: block;
        bottom: 5px;
        top: 30px;
        width: 1px;
        position: absolute;
        left: 0;
    }
    .stage-col,
    .stage-col[stage="lost"], 
    .stage-col[stage="won"] {
        border-top: 2px dashed #d5d5d5;
        padding-top: 1rem !important;
    }
    .stage-col:last-of-type {
        border-bottom: 2px dashed #d5d5d5;
    }
    .stage-col[stage="lost"]:before {
        display: none;
    }
    // > div[style*="overflow:scroll"],
    // > div[style*="overflow: scroll"] {
    //     display: flex;
    // }
}

@media (min-width: 1200px) {
    .automation-canvas .qi-icon-button {
        max-width: 400px;
        min-width: 240px;
    }
}

.bodyIsDragging {
    .triggers-canvas {
        .relation-btn:not(.group-relation-btn) {
            opacity: 0;
        }
    }
}
