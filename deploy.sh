ENV="development";
TGT="staging";

while getopts e: option
do
case "${option}"
    in
    e) ENV=${OPTARG};;
    esac
done

if [ $ENV == "p" ] || [ $ENV == "prod" ] || [ $ENV == "production" ]
then
    DIR="app";
    ENV="production"
    TGT="production";
    CMD="build";
    BRANCH="master";
else
    DIR="dev/app";
    ENV="development";
    TGT="staging";
    CMD="deploy";
    BRANCH="dev";
fi

echo "ENV: $ENV";
echo "DIR: $DIR";
echo "BRANCH: $BRANCH";

if [ $ENV == "production" ]
then
    echo "Are you sure you want to deploy to production? [y/n]"
    read confirm;
    if [ "y" != $confirm ]
    then
        echo "DEPLOYMENT ABORTED! BYE!";
        exit;
    fi
fi

echo "DEPLOYING TO $ENV USING npm $CMD";

echo "setting NODE_OPTIONS=\"--max-old-space-size=2048\"";
export NODE_OPTIONS="--max-old-space-size=2048";

if [ -e dist ]
then
    echo "dist directory already exists. Removing now..."
    rm -r dist;
fi

pwd;
git checkout $BRANCH;
nvm use 12.14.0;
npm run firebase-sync;
npm run $CMD;

cd dist;
wsl zip -r ../dist.zip *;
if [ $? -eq 0 ]
then
    cd ..;
    wsl rsync -vhP dist.zip qiplus:/var/www/html/$DIR;
    if [ $? -eq 0 ]
    then
        ssh qiplus "cd /var/www/html/$DIR; rm -r static; unzip -o dist.zip; ls; exit";
        rm dist.zip;
        if [ $? -eq 0 ]
        then
            rm -r ../qiplus-php/$DIR/static;
            mv -v dist/* ../qiplus-php/$DIR/;
            echo "FINISHED WITH SUCCESS! (DEPLOYED IN $ENV MODE TO $TGT)";
        else
            echo "DEPLOYED TO $ENV BUT COULD NOT CLEAN LOCAL FOLDERS";
        fi
    else
        echo "wsl rsync -vhP dist.zip failed";
    fi
else
    echo "zip failed";
fi
