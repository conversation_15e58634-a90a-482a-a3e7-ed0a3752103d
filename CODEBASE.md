# Índice da Base de Código (Bank-Memory)

Este documento serve como índice e guia de referência rápida da estrutura e contexto do projeto **QIPLUS Frontend**, para facilitar a navegação e compreensão do código.

## Visão Geral

- **Nome do projeto**: QIPLUS Frontend
- **Stack**: React, Redux, Webpack
- **Versão Node recomendada**: v14.x
- Para instruções de instalação, build e execução, consulte o [README.md](README.md).

## Estrutura de Diretórios (Visão Geral)

```text
.
├── README.md
├── package.json
├── jsconfig.json
├── webpack.config.common.js
├── webpack.config.dev.js
├── webpack.config.prod.js
├── webpack.config.staging.js
├── deploy.sh
├── workspace.js
├── qiplus-firebase-sync.js
├── public/
├── src/
├── docs/
└── dist/       (artefatos de build)
```

### Top-level

| Arquivo/Pasta                   | Descrição                                                           |
|---------------------------------|---------------------------------------------------------------------|
| `README.md`                     | Instruções de instalação, build e uso do projeto.                   |
| `package.json`                  | Dependências e scripts NPM.                                         |
| `package-lock.json`             | Versões travadas de dependências NPM.                               |
| `jsconfig.json`                 | Configuração de paths/alias para IDEs (ex: VSCode).                |
| `webpack.config.*.js`           | Configurações do Webpack para diferentes ambientes (common/dev/prod/staging). |
| `deploy.sh`                     | Script de deploy automatizado (staging/production).                |
| `workspace.js`                  | Sincroniza constantes entre front-end e Firebase Functions.         |
| `qiplus-firebase-sync.js`       | Wrapper para executar `workspace.js` em modo manual.               |
| `public/`                       | Arquivos estáticos servidos diretamente (HTML, favicon, etc.).      |
| `dist/`                         | Artefatos de build.                                                 |
| `docs/`                         | Documentação de integração e guias diversos (upgrade, APIs, etc.).  |

## Estrutura da Pasta `src/`

```text
src/
├── App.js            # Componente raiz React
├── index.js          # Ponto de entrada principal
├── actions/          # Redux action creators
├── assets/           # Imagens, estilos, fontes, SVGs, etc.
├── components/       # Componentes React reaproveitáveis
├── components-new/   # Nova versão de componentes em migração
├── constants/        # Definições de constantes (types, erros, collections)
├── container/        # Containers de componentes (smart components)
├── contexts/         # React Context providers/useContext
├── firebase/         # Configuração/inicialização Firebase
├── helpers/          # Funções utilitárias genéricas
├── hooks/            # Custom React Hooks
├── lang/             # Traduções e localização
├── lib/              # Wrappers de bibliotecas externas
├── mailer/           # Configuração de envio de e-mails (Mailgun, etc.)
├── mailgun/          # Integrations específicas com Mailgun
├── models/           # Modelos de dados (schemas/DTOs)
├── reducers/         # Redux reducers
├── repositories/     # Abstração de acesso a dados (API calls)
├── routes/           # Definição de rotas (React Router)
├── services/         # Lógica de negócio e integrações externas
├── store/            # Configuração da store Redux
└── util/             # Utilitários de apoio (formatters, validators)
```

### Detalhamento de Módulos em `src/`

- **actions/**: Redux action creators agrupados por domínio. Funções que disparam tipos de ação e payloads específicos.
- **reducers/**: Funções puras que atualizam o estado global conforme actions recebidas. Cada domínio tem seu reducer e, depois, combinados em `rootReducer`.
- **store/**: Configuração da store Redux, aplicando middlewares (thunk, saga) e integração com DevTools.
- **services/**: Lógica de negócio e integrações externas. Exemplo: `AuthService` para autenticação, `PaymentService` para chamadas de pagamento.
- **repositories/**: Abstração de acesso a dados (API). Importam e configuram axios/fetch, tratam responses e erros, retornam DTOs.
- **components/**: Componentes React puramente apresentados (stateless), focados em renderização. Recebem props e retornam UI.
- **components-new/**: Nova geração de componentes em processo de migração para hooks e padrões atualizados.
- **container/**: Smart components que conectam UI ao Redux ou Context API, lidando com carregamento, dispatch e leitura de estado.
- **hooks/**: Custom React Hooks para reutilização de lógica de estado e efeitos (ex.: `useForm`, `useApi`).
- **contexts/**: Providers e consumers da Context API para temas, usuário, idioma e autenticação.
- **constants/**: Variáveis constantes de tipos de action, códigos de erro, nomes de collections e configurações globais.
- **models/**: Definição de schemas/DTOs e validações de dados (ex.: Yup Schemas, TypeScript interfaces).
- **firebase/**: Configuração e inicialização do Firebase, incluindo Auth, Firestore e sincronização de constantes via `workspace.js`.
- **helpers/** & **util/**: Funções utilitárias genéricas (formatters de data, validações, formatação de moedas).
- **lang/**: Arquivos de tradução e localização (i18n).
- **assets/**: Imagens, SVGs, estilos globais e fontes.
- **lib/**: Wrappers e integrações de bibliotecas externas (ex.: Chart.js, SDKs de terceiros).

## `docs/` (Documentação de domínio)

Arquivos de guias e referências específicas de negócio, integrações e upgrade:

- `docs/QIPlus-upgrade-anual-a_avista.txt`
- `docs/QIPlus-upgrade-anual-parcelado.txt`
- `docs/chat-notazz-api.txt`
- `docs/chat-pagarme-split_rules.txt`
- `docs/pagarme-emails-layout.html`
- `docs/pagarme-emails-assinatura-modo-trial.html`

## Fluxo de Build & Deploy

1. Instalar dependências:
   ```bash
   npm install
   ```
2. Desenvolvimento (hot-reload em https://localhost:3000):
   ```bash
   npm start
   ```
3. Build para produção:
   ```bash
   npm run build
   ```
4. Deploy (via `deploy.sh`):
   ```bash
   bash deploy.sh -e staging    # Deploy em staging
   bash deploy.sh -e production # Deploy em produção
   ```