
*********************************************
Compra Anual Parcelada
*********************************************

parcelas = {
    1:  vAntigo_Anual_Mensalizado,
    2:  vAntigo_Anual_Mensalizado, pagoOk > UPGRADE na metade do ciclo
    3:  vNovo_Anual_Mensalizado,
    4:  vNovo_Anual_Mensalizado,
    5:  vNovo_Anual_Mensalizado,
    6:  vNovo_Anual_Mensalizado,
    7:  vNovo_Anual_Mensalizado,
    8:  vNovo_Anual_Mensalizado,
    9:  vNovo_Anual_Mensalizado,
    10: vNovo_Anual_Mensalizado,
    11: vNovo_Anual_Mensalizado,
    12: vNovo_Anual_Mensalizado,
}

---------------------------------
A - <PERSON><PERSON>o Anual Parcelado
---------------------------------

1 <USER> <GROUP>ção extra: 
    - (cicloRestanteParcial*vNovo_Anual_Mensalizado)-(cicloRestanteParcial*vAntigo_Anual_Mensalizado)
    - POSTBACK_URL "subject=upgrade"
    
2 Aguardar pagamento do boleto atraves da POSTBACK_URL "subject=upgrade"
    - Atualizar Subscription
    - Atualizar Account via PHP
    - Fazer update do cron de parcelamento:
        - Atualizar 'amount'
        - Atualizar split, pasar de porcentagens a amount, preservando o valor da comissão baseado em x% de vAntigo_Anual_Mensalizado


---------------------------------
B - Cartão Anual Parcelado
---------------------------------
parcelas = {
    1:  vAntigo_Anual_Mensalizado, pagoOk
    2:  vAntigo_Anual_Mensalizado, pagoOk > UPGRADE na metade do ciclo
    3:  vAntigo_Anual_Mensalizado,   | Estorno
    4:  vAntigo_Anual_Mensalizado,   | Estorno
    5:  vAntigo_Anual_Mensalizado,   | Estorno
    6:  vAntigo_Anual_Mensalizado,   | Estorno
    7:  vAntigo_Anual_Mensalizado,   | Estorno
    8:  vAntigo_Anual_Mensalizado,   | Estorno
    9:  vAntigo_Anual_Mensalizado,   | Estorno
    10: vAntigo_Anual_Mensalizado,   | Estorno
    11: vAntigo_Anual_Mensalizado,   | Estorno
    12: vAntigo_Anual_Mensalizado,   | Estorno
}

1 - Estorno: ciclosRestantes x vAntigo_Anual_Mensalizado
2 - Transação extra: (cicloRestanteParcial*vNovo_Anual_Mensalizado)-(cicloRestanteParcial*vAntigo_Anual_Mensalizado),
2 - Novo Parcelamento: valorTotal (ciclosRestantes x vNovo_Anual_Mensalizado) em [ciclosRestantes] installments 
