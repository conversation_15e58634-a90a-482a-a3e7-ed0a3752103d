*********************************************
Compra Anual à Vista
*********************************************

compraAntiga = vAntigo_Anual_Mensalizado*12;
compraNova = vNovo_Anual_Mensalizado*12;

valorDif = (cicloRestanteParcial*vNovo_Anual_Mensalizado*12) - (cicloRestanteParcial*vAntigo_Anual_Mensalizado*12);

Opção 1:
1 - Estorno: cicloRestanteParcial*(vAntigo_Anual_Mensalizado*12)
2 - Transação nova: (cicloRestanteParcial*(vNovo_Anual_Mensalizado*12))

Opção 2:
1 - Transação nova: valorDif

---------------------------------
A - <PERSON><PERSON>o Anual à Vista
---------------------------------
1 <USER> <GROUP>ção nova:
    - valorDif
    - POSTBACK_URL "subject=upgrade"
    - metadata: {
        accountId,
        plan: planoNovo,
    }
    
2 - Aguardar pagamento do boleto atraves da POSTBACK_URL "subject=upgrade"
    - Atualizar Subscription
    - Atualizar Conta no PHP

---------------------------------
B - Cartão Anual à Vista
---------------------------------
1 <USER> <GROUP> se usamos Opção 1 ou Opção 2
2 - Fazer transação
3 - Se resposta é 'paid'
    - Atualizar Subscription
    - Atualizar Conta no PHP