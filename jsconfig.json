{"compilerOptions": {"baseUrl": ".", "jsx": "react", "paths": {"Actions/*": ["./src/actions/*"], "Actions": ["./src/actions/index"], "Assets/*": ["./src/assets/*"], "Components/*": ["./src/components/*"], "Constants": ["./src/constants/index"], "Constants/*": ["./src/constants/*"], "FirebaseRef": ["./src/firebase/index"], "FirebaseRef/*": ["./src/firebase/*"], "Helpers": ["./src/helpers/index"], "Helpers/*": ["./src/helpers/*"], "Lang/*": ["./src/lang/*"], "Models/*": ["./src/models/*"], "Routes/*": ["./src/routes/*"], "UIComponents/*": ["./src/components/UIComponents/*"], "Util/*": ["./src/util/*"], "Widgets/*": ["./src/components/Widgets/*"], "Repositories/*": ["./src/repositories/*"], "Hooks/*": ["./src/hooks/*"]}}, "include": ["./src/**/*"], "exclude": ["./node_modules", "./public", "./docs", "./credentials"]}