{"name": "qiplus", "version": "2.0.0", "description": "QIPlus React App", "scripts": {"firebase-sync": "node ./qiplus-firebase-sync", "start": "webpack-dev-server --mode development --inline --progress --env=dev --https --cert ./server.crt --key ./server.key", "deploy": "webpack --mode development --env=staging", "build": "webpack --mode production --env=prod", "lint": "npx eslint src/", "lint-fix": "npx eslint --fix src/", "prettier-fix": "npx prettier --write src/"}, "keywords": ["reactjs"], "author": "<PERSON><PERSON>", "license": "ISC", "devDependencies": {"@babel/core": "^7.15.8", "@babel/eslint-parser": "^7.5.4", "@babel/plugin-proposal-logical-assignment-operators": "^7.14.5", "@babel/polyfill": "^7.12.1", "@babel/preset-env": "^7.15.8", "@babel/preset-react": "^7.14.5", "@babel/preset-stage-2": "^7.8.3", "babel-loader": "^7.1.5", "clean-webpack-plugin": "^0.1.19", "css-loader": "^0.28.11", "dotenv-webpack": "^7.0.3", "eslint": "^8.57.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "extract-text-webpack-plugin": "^3.0.2", "file-loader": "^1.1.11", "friendly-errors-webpack-plugin": "^1.7.0", "html-loader": "^0.5.5", "html-webpack-plugin": "^3.2.0", "mini-css-extract-plugin": "^0.4.0", "node-sass": "^6.0.1", "prettier": "3.2.5", "react": "^16.8.6", "react-dom": "^16.8.6", "redux-devtools-extension": "^2.13.8", "request": "^2.79.0", "sass-loader": "^6.0.7", "uglifyjs-webpack-plugin": "^1.2.5", "url-loader": "^1.0.1", "webpack": "^4.46.0", "webpack-bundle-analyzer": "^2.11.1", "webpack-cli": "^2.0.14", "webpack-dev-server": "^3.2.1"}, "dependencies": {"@amcharts/amcharts4": "^4.5.9", "@date-io/date-fns": "^1.3.9", "@date-io/moment": "^1.3.9", "@fullcalendar/core": "^4.3.0", "@fullcalendar/daygrid": "^4.3.0", "@fullcalendar/google-calendar": "^4.3.0", "@fullcalendar/interaction": "^4.3.0", "@fullcalendar/list": "^4.3.0", "@fullcalendar/react": "^4.3.0", "@fullcalendar/resource-timeline": "^4.3.0", "@fullcalendar/timegrid": "^4.3.0", "@fullcalendar/timeline": "^4.3.0", "@material-ui/core": "^4.9.0", "@material-ui/icons": "^4.2.1", "@material-ui/lab": "^4.0.0-alpha.40", "@material-ui/pickers": "^3.2.3", "@microsoft/microsoft-graph-client": "^2.0.0", "@react-google-maps/api": "^2.20.3", "@react-oauth/google": "^0.12.1", "auth0-js": "^9.11.2", "autosuggest-highlight": "3.1.1", "axios": "^0.19.0", "canvas-datagrid": "^0.22.13", "chart.js": "^2.8.0", "ckeditor4-react": "^2.1.0", "classnames": "^2.2.6", "downshift": "^3.2.10", "draft-js": "^0.11.0", "draftjs-to-html": "^0.9.1", "firebase": "^8.6.3", "fs": "0.0.1-security", "globalize": "^0.1.1", "google-map-react": "^1.1.4", "grapesjs": "^0.18.4", "grapesjs-preset-newsletter": "^1.0.2", "hls.js": "^0.12.4", "html-to-draftjs": "^1.5.0", "http-proxy": "^1.18.1", "instantsearch.css": "^7.3.1", "javascript-obfuscator": "^4.1.1", "jquery": "^3.4.1", "leaflet": "^1.5.1", "lucide-react": "^0.460.0", "moment": "^2.24.0", "msal": "^1.3.4", "mui-datatables": "^2.8.1", "paper": "^0.12.15", "prettier-plugin-multiline-arrays": "^3.0.4", "qrcode.react": "^3.1.0", "quoted-printable": "^1.0.1", "rc-queue-anim": "^1.6.12", "react-addons-update": "15.6.2", "react-autosuggest": "^9.4.3", "react-beautiful-dnd": "^11.0.5", "react-big-calendar": "^0.19.0", "react-bootstrap-sweetalert": "^4.4.1", "react-chartjs-2": "^2.7.6", "react-color": "^2.17.3", "react-content-loader": "^4.2.2", "react-countup": "^4.2.0", "react-credit-cards": "^0.7.0", "react-cropper": "^1.2.0", "react-custom-scrollbars": "^4.2.1", "react-d3-speedometer": "^0.6.1", "react-dnd": "^9.3.2", "react-dnd-html5-backend": "^9.3.2", "react-draft-wysiwyg": "^1.13.2", "react-dragula": "1.1.17", "react-dropzone-component": "^3.2.0", "react-flags-select": "^1.1.13", "react-google-charts": "^4.0.1", "react-gtm-module": "^2.0.11", "react-helmet": "^5.2.1", "react-hot-loader": "^4.8.0", "react-icons": "^4.10.1", "react-instantsearch": "^5.7.0", "react-intl": "^2.9.0", "react-joyride": "^1.11.4", "react-jvectormap": "0.0.12", "react-leaflet": "^2.4.0", "react-loadable": "^5.5.0", "react-loading": "^2.0.3", "react-notifications": "^1.4.3", "react-number-format": "^4.0.8", "react-player": "^2.9.0", "react-qr-code": "^2.0.5", "react-quill": "^1.3.3", "react-redux": "^7.1.0", "react-router-dom": "^5.0.1", "react-select": "^3.0.4", "react-sidebar": "^3.0.2", "react-slick": "^0.25.2", "react-spinners": "^0.13.8", "react-star-rating-component": "^1.4.1", "react-swipeable-views": "^0.13.3", "react-table": "^6.10.0", "react-text-mask": "^5.4.3", "react-tippy": "^1.4.0", "react-toggle-switch": "^3.0.4", "react-tooltip": "^3.10.0", "react-twitter-widgets": "^1.7.1", "reactstrap": "^8.0.1", "recharts": "^1.6.2", "redux": "^4.0.4", "redux-thunk": "^2.3.0", "screenfull": "^4.2.1", "slick-carousel": "^1.8.1", "socket.io-client": "^4.4.1", "styled-components": "^6.1.0", "utf8": "^3.0.0", "video-react": "^0.14.1", "weather-icons": "^1.3.2", "xlsx": "^0.16.0"}, "resolutions": {"styled-components": "^5"}}